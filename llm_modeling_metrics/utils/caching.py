"""
Comprehensive caching system for LLM modeling metrics.

This module provides caching utilities for configurations, computation results,
and other frequently accessed data with TTL and invalidation logic.
"""

import gc
import hashlib
import json
import os
import pickle
import threading
import time
import weakref
from dataclasses import asdict, dataclass
from datetime import datetime, timed<PERSON>ta
from pathlib import Path
from typing import Any, Callable, Dict, Generic, Optional, TypeVar, Union

T = TypeVar("T")


@dataclass
class CacheEntry:
    """Represents a single cache entry with metadata."""

    data: Any
    created_at: float
    last_accessed: float
    access_count: int
    ttl: Optional[float] = None
    size_bytes: Optional[int] = None

    def is_expired(self) -> bool:
        """Check if the cache entry has expired."""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl

    def is_stale(self, max_age: float) -> bool:
        """Check if the cache entry is stale based on max age."""
        return time.time() - self.created_at > max_age

    def touch(self) -> None:
        """Update last accessed time and increment access count."""
        self.last_accessed = time.time()
        self.access_count += 1


class MemoryCache(Generic[T]):
    """In-memory cache with TTL and LRU eviction."""

    def __init__(self, max_size: int = 1000, default_ttl: Optional[float] = None):
        """
        Initialize memory cache.

        Args:
            max_size: Maximum number of entries to keep in cache
            default_ttl: Default TTL in seconds for cache entries
        """
        self.max_size = max_size
        self.default_ttl = default_ttl
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()
        self._stats = {"hits": 0, "misses": 0, "evictions": 0, "size": 0}

    def get(self, key: str) -> Optional[T]:
        """Get value from cache."""
        with self._lock:
            if key not in self._cache:
                self._stats["misses"] += 1
                return None

            entry = self._cache[key]
            if entry.is_expired():
                del self._cache[key]
                self._stats["misses"] += 1
                return None

            entry.touch()
            self._stats["hits"] += 1
            return entry.data

    def put(self, key: str, value: T, ttl: Optional[float] = None) -> None:
        """Put value into cache."""
        with self._lock:
            # Use provided TTL or default
            effective_ttl = ttl if ttl is not None else self.default_ttl

            # Calculate size estimate
            size_bytes = self._estimate_size(value)

            entry = CacheEntry(
                data=value,
                created_at=time.time(),
                last_accessed=time.time(),
                access_count=1,
                ttl=effective_ttl,
                size_bytes=size_bytes,
            )

            self._cache[key] = entry
            self._stats["size"] = len(self._cache)

            # Evict if necessary
            if len(self._cache) > self.max_size:
                self._evict_lru()

    def delete(self, key: str) -> bool:
        """Delete entry from cache."""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats["size"] = len(self._cache)
                return True
            return False

    def clear(self) -> None:
        """Clear all entries from cache."""
        with self._lock:
            self._cache.clear()
            self._stats["size"] = 0

    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed entries."""
        with self._lock:
            expired_keys = [
                key for key, entry in self._cache.items() if entry.is_expired()
            ]

            for key in expired_keys:
                del self._cache[key]

            self._stats["size"] = len(self._cache)
            return len(expired_keys)

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0

            return {
                **self._stats,
                "hit_rate": hit_rate,
                "total_requests": total_requests,
                "memory_usage_bytes": sum(
                    entry.size_bytes or 0 for entry in self._cache.values()
                ),
            }

    def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if not self._cache:
            return

        # Find LRU entry
        lru_key = min(self._cache.keys(), key=lambda k: self._cache[k].last_accessed)

        del self._cache[lru_key]
        self._stats["evictions"] += 1
        self._stats["size"] = len(self._cache)

    def _estimate_size(self, obj: Any) -> int:
        """Estimate size of object in bytes."""
        try:
            return len(pickle.dumps(obj))
        except Exception:
            # Fallback estimation
            return 1024  # 1KB default


class DiskCache:
    """Persistent disk-based cache with TTL support."""

    def __init__(self, cache_dir: str, default_ttl: Optional[float] = None):
        """
        Initialize disk cache.

        Args:
            cache_dir: Directory to store cache files
            default_ttl: Default TTL in seconds for cache entries
        """
        self.cache_dir = Path(cache_dir)
        self.default_ttl = default_ttl
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self._lock = threading.RLock()
        self._stats = {"hits": 0, "misses": 0, "writes": 0, "size": 0}

    def get(self, key: str) -> Optional[Any]:
        """Get value from disk cache."""
        with self._lock:
            cache_file = self._get_cache_file(key)

            if not cache_file.exists():
                self._stats["misses"] += 1
                return None

            try:
                with open(cache_file, "rb") as f:
                    entry_data = pickle.load(f)

                entry = CacheEntry(**entry_data)

                if entry.is_expired():
                    cache_file.unlink()
                    self._stats["misses"] += 1
                    return None

                # Update access time
                entry.touch()
                with open(cache_file, "wb") as f:
                    pickle.dump(asdict(entry), f)

                self._stats["hits"] += 1
                return entry.data

            except Exception as e:
                print(f"Error reading cache file {cache_file}: {e}")
                # Remove corrupted cache file
                try:
                    cache_file.unlink()
                except Exception:
                    pass
                self._stats["misses"] += 1
                return None

    def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """Put value into disk cache."""
        with self._lock:
            cache_file = self._get_cache_file(key)
            effective_ttl = ttl if ttl is not None else self.default_ttl

            try:
                entry = CacheEntry(
                    data=value,
                    created_at=time.time(),
                    last_accessed=time.time(),
                    access_count=1,
                    ttl=effective_ttl,
                    size_bytes=None,
                )

                with open(cache_file, "wb") as f:
                    pickle.dump(asdict(entry), f)

                self._stats["writes"] += 1
                self._update_size_stats()

            except Exception as e:
                print(f"Error writing cache file {cache_file}: {e}")

    def delete(self, key: str) -> bool:
        """Delete entry from disk cache."""
        with self._lock:
            cache_file = self._get_cache_file(key)

            if cache_file.exists():
                try:
                    cache_file.unlink()
                    self._update_size_stats()
                    return True
                except Exception as e:
                    print(f"Error deleting cache file {cache_file}: {e}")

            return False

    def clear(self) -> None:
        """Clear all entries from disk cache."""
        with self._lock:
            try:
                for cache_file in self.cache_dir.glob("*.cache"):
                    cache_file.unlink()
                self._stats["size"] = 0
            except Exception as e:
                print(f"Error clearing cache: {e}")

    def cleanup_expired(self) -> int:
        """Remove expired entries and return count of removed entries."""
        with self._lock:
            removed_count = 0

            for cache_file in self.cache_dir.glob("*.cache"):
                try:
                    with open(cache_file, "rb") as f:
                        entry_data = pickle.load(f)

                    entry = CacheEntry(**entry_data)

                    if entry.is_expired():
                        cache_file.unlink()
                        removed_count += 1

                except Exception:
                    # Remove corrupted files
                    try:
                        cache_file.unlink()
                        removed_count += 1
                    except Exception:
                        pass

            self._update_size_stats()
            return removed_count

    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        with self._lock:
            total_requests = self._stats["hits"] + self._stats["misses"]
            hit_rate = self._stats["hits"] / total_requests if total_requests > 0 else 0

            # Calculate disk usage
            disk_usage = sum(
                f.stat().st_size for f in self.cache_dir.glob("*.cache") if f.exists()
            )

            return {
                **self._stats,
                "hit_rate": hit_rate,
                "total_requests": total_requests,
                "disk_usage_bytes": disk_usage,
            }

    def _get_cache_file(self, key: str) -> Path:
        """Get cache file path for key."""
        # Create a safe filename from the key
        safe_key = hashlib.md5(key.encode()).hexdigest()
        return self.cache_dir / f"{safe_key}.cache"

    def _update_size_stats(self) -> None:
        """Update size statistics."""
        self._stats["size"] = len(list(self.cache_dir.glob("*.cache")))


class ComputationCache:
    """Cache for computation results with automatic key generation."""

    def __init__(
        self,
        cache_dir: str = "./cache/computations",
        memory_cache_size: int = 100,
        default_ttl: float = 3600,
    ):  # 1 hour default
        """
        Initialize computation cache.

        Args:
            cache_dir: Directory for disk cache
            memory_cache_size: Size of in-memory cache
            default_ttl: Default TTL in seconds
        """
        self.memory_cache = MemoryCache[Any](memory_cache_size, default_ttl)
        self.disk_cache = DiskCache(cache_dir, default_ttl)
        self._lock = threading.RLock()

    def get_or_compute(
        self,
        func: Callable[..., T],
        *args,
        ttl: Optional[float] = None,
        use_disk: bool = True,
        **kwargs,
    ) -> T:
        """
        Get cached result or compute and cache it.

        Args:
            func: Function to compute result
            *args: Function arguments
            ttl: TTL for cache entry
            use_disk: Whether to use disk cache
            **kwargs: Function keyword arguments

        Returns:
            Computed or cached result
        """
        # Generate cache key
        cache_key = self._generate_key(func, args, kwargs)

        # Try memory cache first
        result = self.memory_cache.get(cache_key)
        if result is not None:
            return result

        # Try disk cache if enabled
        if use_disk:
            result = self.disk_cache.get(cache_key)
            if result is not None:
                # Store in memory cache for faster access
                self.memory_cache.put(cache_key, result, ttl)
                return result

        # Compute result
        result = func(*args, **kwargs)

        # Cache the result
        self.memory_cache.put(cache_key, result, ttl)
        if use_disk:
            self.disk_cache.put(cache_key, result, ttl)

        return result

    def invalidate(self, func: Callable, *args, **kwargs) -> None:
        """Invalidate cached result for specific function call."""
        cache_key = self._generate_key(func, args, kwargs)
        self.memory_cache.delete(cache_key)
        self.disk_cache.delete(cache_key)

    def clear_all(self) -> None:
        """Clear all cached results."""
        self.memory_cache.clear()
        self.disk_cache.clear()

    def cleanup_expired(self) -> Dict[str, int]:
        """Clean up expired entries from both caches."""
        memory_removed = self.memory_cache.cleanup_expired()
        disk_removed = self.disk_cache.cleanup_expired()

        return {"memory_removed": memory_removed, "disk_removed": disk_removed}

    def get_stats(self) -> Dict[str, Any]:
        """Get comprehensive cache statistics."""
        return {
            "memory_cache": self.memory_cache.get_stats(),
            "disk_cache": self.disk_cache.get_stats(),
        }

    def _generate_key(self, func: Callable, args: tuple, kwargs: dict) -> str:
        """Generate cache key for function call."""
        # Create a deterministic key from function name and arguments
        func_name = f"{func.__module__}.{func.__name__}"

        # Convert arguments to hashable representation
        key_data = {
            "function": func_name,
            "args": self._make_hashable(args),
            "kwargs": self._make_hashable(kwargs),
        }

        key_str = json.dumps(key_data, sort_keys=True, default=str)
        return hashlib.sha256(key_str.encode()).hexdigest()

    def _make_hashable(self, obj: Any) -> Any:
        """Convert object to hashable representation."""
        if isinstance(obj, dict):
            return tuple(sorted((k, self._make_hashable(v)) for k, v in obj.items()))
        elif isinstance(obj, (list, tuple)):
            return tuple(self._make_hashable(item) for item in obj)
        elif isinstance(obj, set):
            return tuple(sorted(self._make_hashable(item) for item in obj))
        else:
            return obj


class CacheManager:
    """Central cache manager for coordinating different cache types."""

    def __init__(self, base_cache_dir: str = "./cache"):
        """
        Initialize cache manager.

        Args:
            base_cache_dir: Base directory for all caches
        """
        self.base_cache_dir = Path(base_cache_dir)
        self.base_cache_dir.mkdir(parents=True, exist_ok=True)

        # Initialize different cache types
        self.config_cache = DiskCache(
            str(self.base_cache_dir / "configs"), default_ttl=30 * 24 * 3600  # 30 days
        )

        self.computation_cache = ComputationCache(
            str(self.base_cache_dir / "computations"),
            memory_cache_size=200,
            default_ttl=24 * 3600,  # 24 hours
        )

        self.model_cache = MemoryCache[Any](max_size=50, default_ttl=3600)  # 1 hour

        # Background cleanup thread
        self._cleanup_thread = None
        self._stop_cleanup = threading.Event()
        self.start_background_cleanup()

    def start_background_cleanup(self, interval: int = 3600) -> None:
        """Start background cleanup thread."""
        if self._cleanup_thread and self._cleanup_thread.is_alive():
            return

        def cleanup_worker():
            while not self._stop_cleanup.wait(interval):
                try:
                    self.cleanup_expired()
                except Exception as e:
                    print(f"Error in background cleanup: {e}")

        self._cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        self._cleanup_thread.start()

    def stop_background_cleanup(self) -> None:
        """Stop background cleanup thread."""
        self._stop_cleanup.set()
        if self._cleanup_thread:
            self._cleanup_thread.join(timeout=5)

    def cleanup_expired(self) -> Dict[str, Any]:
        """Clean up expired entries from all caches."""
        results = {}

        try:
            results["config_cache"] = self.config_cache.cleanup_expired()
        except Exception as e:
            results["config_cache"] = f"Error: {e}"

        try:
            results["computation_cache"] = self.computation_cache.cleanup_expired()
        except Exception as e:
            results["computation_cache"] = f"Error: {e}"

        try:
            results["model_cache"] = self.model_cache.cleanup_expired()
        except Exception as e:
            results["model_cache"] = f"Error: {e}"

        # Force garbage collection
        gc.collect()

        return results

    def clear_all(self) -> None:
        """Clear all caches."""
        self.config_cache.clear()
        self.computation_cache.clear_all()
        self.model_cache.clear()
        gc.collect()

    def get_comprehensive_stats(self) -> Dict[str, Any]:
        """Get statistics from all caches."""
        return {
            "config_cache": self.config_cache.get_stats(),
            "computation_cache": self.computation_cache.get_stats(),
            "model_cache": self.model_cache.get_stats(),
            "system": {
                "cache_dir_size": self._get_directory_size(self.base_cache_dir),
                "cleanup_thread_active": self._cleanup_thread
                and self._cleanup_thread.is_alive(),
            },
        }

    def _get_directory_size(self, path: Path) -> int:
        """Get total size of directory in bytes."""
        try:
            return sum(f.stat().st_size for f in path.rglob("*") if f.is_file())
        except Exception:
            return 0

    def __del__(self):
        """Cleanup when manager is destroyed."""
        self.stop_background_cleanup()


# Global cache manager instance
_cache_manager = None
_cache_manager_lock = threading.Lock()


def get_cache_manager() -> CacheManager:
    """Get global cache manager instance (singleton)."""
    global _cache_manager

    if _cache_manager is None:
        with _cache_manager_lock:
            if _cache_manager is None:
                _cache_manager = CacheManager()

    return _cache_manager


# Decorator for caching function results
def cached(ttl: Optional[float] = None, use_disk: bool = True):
    """
    Decorator to cache function results.

    Args:
        ttl: TTL for cache entry in seconds
        use_disk: Whether to use disk cache
    """

    def decorator(func: Callable[..., T]) -> Callable[..., T]:
        def wrapper(*args, **kwargs) -> T:
            cache_manager = get_cache_manager()
            return cache_manager.computation_cache.get_or_compute(
                func, *args, ttl=ttl, use_disk=use_disk, **kwargs
            )

        return wrapper

    return decorator
