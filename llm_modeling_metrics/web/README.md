# LLM Modeling Metrics Web API

A FastAPI-based web service for analyzing computational requirements and performance characteristics of Large Language Models.

## Features

- **Model Analysis**: Comprehensive analysis of LLM computational requirements
- **Real-time Updates**: WebSocket support for progress tracking
- **Rate Limiting**: Built-in rate limiting to prevent abuse
- **Authentication**: Optional API token authentication
- **Error Handling**: Comprehensive error handling with detailed responses
- **Monitoring**: Built-in statistics and monitoring endpoints

## Quick Start

### 1. Install Dependencies

```bash
pip install fastapi uvicorn websockets psutil
```

### 2. Start the Server

```bash
python run_api.py
```

The API will be available at `http://localhost:8000`

### 3. View Documentation

- Interactive API docs: `http://localhost:8000/docs`
- ReDoc documentation: `http://localhost:8000/redoc`

## API Endpoints

### Core Endpoints

- `GET /` - API information
- `GET /health` - Health check
- `POST /api/analyze` - Analyze models
- `GET /api/models/supported` - List supported architectures
- `GET /api/models/validate/{model_name}` - Validate model support

### Real-time Features

- `WebSocket /ws/{client_id}` - Real-time analysis updates
- `GET /api/analysis/status/{request_id}` - Get analysis status
- `GET /api/analysis/list` - List analysis requests
- `DELETE /api/analysis/{request_id}` - Cancel analysis

### Monitoring

- `GET /api/stats` - API usage statistics

## Configuration

### Environment Variables

- `HOST` - Server host (default: 0.0.0.0)
- `PORT` - Server port (default: 8000)
- `API_TOKEN` - Optional API token for authentication
- `RATE_LIMIT_REQUESTS` - Requests per window (default: 10)
- `RATE_LIMIT_WINDOW` - Rate limit window in seconds (default: 60)
- `DEBUG` - Enable debug mode (default: false)

### Example with Authentication

```bash
export API_TOKEN="your-secret-token"
python run_api.py
```

## Usage Examples

### Analyze a Single Model

```python
import requests

response = requests.post("http://localhost:8000/api/analyze", json={
    "model_names": ["meta-llama/Llama-3.2-1B-Instruct"],
    "sequence_length": 2048,
    "batch_size": 1,
    "precision": "fp16"
})

result = response.json()
print(f"Total parameters: {result['results']['meta-llama/Llama-3.2-1B-Instruct']['total_params']:,}")
```

### Compare Multiple Models

```python
response = requests.post("http://localhost:8000/api/analyze", json={
    "model_names": [
        "meta-llama/Llama-3.2-1B-Instruct",
        "meta-llama/Llama-3.2-3B-Instruct"
    ],
    "sequence_length": 2048,
    "include_comparison": True
})

comparison = response.json()["comparison"]
print(f"Models compared: {comparison['models']}")
```

### WebSocket Real-time Updates

```python
import asyncio
import websockets
import json

async def listen_for_updates():
    uri = "ws://localhost:8000/ws/my_client_id"
    async with websockets.connect(uri) as websocket:
        while True:
            message = await websocket.recv()
            data = json.loads(message)
            print(f"Update: {data}")

asyncio.run(listen_for_updates())
```

### With Authentication

```python
headers = {"Authorization": "Bearer your-secret-token"}
response = requests.post(
    "http://localhost:8000/api/analyze",
    json={"model_names": ["meta-llama/Llama-3.2-1B-Instruct"]},
    headers=headers
)
```

## Testing

Run the test suite:

```bash
python test_web_api.py
```

This will test:
- Health endpoint
- Supported models endpoint
- Model validation
- Rate limiting
- WebSocket connections
- Analysis endpoint

## Error Handling

The API provides detailed error responses with:
- Error message
- HTTP status code
- Timestamp
- Request ID for tracking
- Additional details (in debug mode)

Example error response:
```json
{
    "error": "Model not supported: unknown architecture",
    "status_code": 400,
    "timestamp": "2024-01-15T10:30:00Z",
    "request_id": "error_1705312200_12345"
}
```

## Rate Limiting

Default rate limits:
- 10 requests per 60 seconds per IP address
- Configurable via environment variables
- Returns HTTP 429 when exceeded

## WebSocket Events

The WebSocket connection supports these event types:

- `analysis_started` - Analysis has begun
- `progress_update` - Progress percentage update
- `analysis_completed` - Analysis finished successfully
- `analysis_failed` - Analysis failed with error
- `analysis_cancelled` - Analysis was cancelled
- `ping`/`pong` - Connection health check

## Production Deployment

For production deployment:

1. Set appropriate environment variables
2. Use a production WSGI server (gunicorn, etc.)
3. Configure reverse proxy (nginx)
4. Set up proper logging
5. Use Redis for rate limiting storage
6. Configure database for analysis status storage

Example with gunicorn:
```bash
gunicorn llm_modeling_metrics.web.app:app -w 4 -k uvicorn.workers.UvicornWorker
```
