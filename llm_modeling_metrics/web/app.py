"""
FastAPI web application for LLM modeling metrics analysis.
"""

import asyncio
import json
import logging
import os
import time
import traceback
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union

from fastapi import (
    BackgroundTasks,
    Depends,
    FastAPI,
    HTTPException,
    Request,
    WebSocket,
    WebSocketDisconnect,
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse, JSONResponse
from fastapi.security import HTTPAuthorizationCredentials, HTTPBearer
from fastapi.staticfiles import StaticFiles
from pydantic import BaseModel, Field, validator

from ..comparison.comparator import Comparator, ComparisonResult
from ..core.base_model import ModelMetrics, ParallelConfig
from ..core.model_factory import (
    ConfigurationError,
    ModelFactory,
    ModelNotSupportedError,
)
from .models import (
    AnalysisStatus,
    ComparisonResultModel,
    ErrorResponse,
    KVGrowthAnalysisRequest,
    KVGrowthPoint,
    MemoryAnalysisRequest,
    MemoryAnalysisResponse,
    MemoryBreakdown,
    MixedPrecisionMemoryBreakdown,
    ModelAnalysisRequest,
    ModelAnalysisResponse,
    ModelMetricsModel,
    SupportedModelsResponse,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="LLM Modeling Metrics API",
    description="API for analyzing computational requirements and performance characteristics of Large Language Models",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Mount static files
import os

static_dir = os.path.join(os.path.dirname(__file__), "static")
if os.path.exists(static_dir):
    app.mount("/static", StaticFiles(directory=static_dir), name="static")

# Initialize components and register models
from ..models.dense_model import DenseModel
from ..models.moe_model import MoEModel

model_factory = ModelFactory()

# Register model architectures - simplified to two types: dense and moe
# model_factory.register_model('dense', DenseModel)  # All dense models (Llama, Qwen, Mistral, etc.)
# model_factory.register_model('moe', MoEModel)      # All MoE models (DeepSeek, Mixtral, etc.)

comparator = Comparator()


# Rate limiting function
def check_rate_limit(client_ip: str) -> bool:
    """
    Check if client has exceeded rate limit.

    Args:
        client_ip: Client IP address

    Returns:
        True if within rate limit, False if exceeded
    """
    now = time.time()
    client_requests = rate_limit_storage[client_ip]

    # Remove old requests outside the window
    while client_requests and client_requests[0] < now - RATE_LIMIT_WINDOW:
        client_requests.popleft()

    # Check if limit exceeded
    if len(client_requests) >= RATE_LIMIT_REQUESTS:
        return False

    # Add current request
    client_requests.append(now)
    return True


def get_client_ip(request: Request) -> str:
    """Get client IP address from request."""
    # Check for forwarded headers (when behind proxy)
    forwarded_for = request.headers.get("X-Forwarded-For")
    if forwarded_for:
        return forwarded_for.split(",")[0].strip()

    real_ip = request.headers.get("X-Real-IP")
    if real_ip:
        return real_ip

    # Fallback to direct connection
    return request.client.host if request.client else "unknown"


async def rate_limit_dependency(request: Request):
    """Dependency for rate limiting."""
    client_ip = get_client_ip(request)

    if not check_rate_limit(client_ip):
        raise HTTPException(
            status_code=429,
            detail=f"Rate limit exceeded. Maximum {RATE_LIMIT_REQUESTS} requests per {RATE_LIMIT_WINDOW} seconds.",
            headers={"Retry-After": str(RATE_LIMIT_WINDOW)},
        )

    return client_ip


# In-memory storage for analysis status (use Redis/database in production)
analysis_status: Dict[str, AnalysisStatus] = {}

# Rate limiting storage (use Redis in production)
rate_limit_storage: Dict[str, deque] = defaultdict(lambda: deque())


# WebSocket connection manager
class ConnectionManager:
    """Manages WebSocket connections for real-time updates."""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}

    async def connect(self, websocket: WebSocket, client_id: str):
        """Accept a WebSocket connection."""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"WebSocket client {client_id} connected")

    def disconnect(self, client_id: str):
        """Remove a WebSocket connection."""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"WebSocket client {client_id} disconnected")

    async def send_personal_message(self, message: dict, client_id: str):
        """Send a message to a specific client."""
        if client_id in self.active_connections:
            try:
                await self.active_connections[client_id].send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error sending message to {client_id}: {e}")
                self.disconnect(client_id)

    async def broadcast(self, message: dict):
        """Broadcast a message to all connected clients."""
        disconnected_clients = []
        for client_id, connection in self.active_connections.items():
            try:
                await connection.send_text(json.dumps(message))
            except Exception as e:
                logger.error(f"Error broadcasting to {client_id}: {e}")
                disconnected_clients.append(client_id)

        # Clean up disconnected clients
        for client_id in disconnected_clients:
            self.disconnect(client_id)


manager = ConnectionManager()

# Rate limiting configuration
RATE_LIMIT_REQUESTS = int(
    os.getenv("RATE_LIMIT_REQUESTS", "100")
)  # requests per window
RATE_LIMIT_WINDOW = int(os.getenv("RATE_LIMIT_WINDOW", "60"))  # window in seconds

# API token configuration
API_TOKEN = os.getenv("API_TOKEN")  # Optional API token
security = HTTPBearer(auto_error=False) if API_TOKEN else None


def verify_token_dependency():
    """Create token verification dependency based on configuration."""
    if API_TOKEN is None:
        # No authentication required
        async def no_auth():
            return True

        return no_auth
    else:
        # Authentication required
        async def verify_token(
            credentials: HTTPAuthorizationCredentials = Depends(security),
        ):
            if credentials.credentials != API_TOKEN:
                raise HTTPException(
                    status_code=401,
                    detail="Invalid API token",
                    headers={"WWW-Authenticate": "Bearer"},
                )
            return True

        return verify_token


# Create the dependency
verify_token = verify_token_dependency()


@app.get("/")
async def root():
    """Serve the main dashboard HTML file."""
    static_dir = os.path.join(os.path.dirname(__file__), "static")
    index_path = os.path.join(static_dir, "index.html")

    if os.path.exists(index_path):
        return FileResponse(index_path)
    else:
        # Fallback to API information if static files not available
        return {
            "name": "LLM Modeling Metrics API",
            "version": "1.0.0",
            "description": "API for analyzing computational requirements of Large Language Models",
            "docs": "/docs",
            "health": "/health",
        }


@app.get("/api", response_model=Dict[str, str])
async def api_root():
    """API root endpoint with information."""
    return {
        "name": "LLM Modeling Metrics API",
        "version": "1.0.0",
        "description": "API for analyzing computational requirements of Large Language Models",
        "docs": "/docs",
        "health": "/health",
    }


@app.get("/health", response_model=Dict[str, str])
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0",
    }


@app.websocket("/ws/{client_id}")
async def websocket_endpoint(websocket: WebSocket, client_id: str):
    """
    WebSocket endpoint for real-time analysis progress updates.

    Clients can connect to receive real-time updates about analysis progress,
    including status changes, progress percentages, and completion notifications.

    Args:
        websocket: WebSocket connection
        client_id: Unique identifier for the client
    """
    await manager.connect(websocket, client_id)
    try:
        while True:
            # Keep connection alive and handle incoming messages
            data = await websocket.receive_text()
            message = json.loads(data)

            # Handle ping/pong for connection health
            if message.get("type") == "ping":
                await manager.send_personal_message(
                    {"type": "pong", "timestamp": datetime.now().isoformat()}, client_id
                )

    except WebSocketDisconnect:
        manager.disconnect(client_id)
    except Exception as e:
        logger.error(f"WebSocket error for client {client_id}: {e}")
        manager.disconnect(client_id)


@app.post("/api/analyze", response_model=ModelAnalysisResponse)
async def analyze_models(
    request: ModelAnalysisRequest,
    background_tasks: BackgroundTasks,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> ModelAnalysisResponse:
    """
    Analyze multiple models and return comprehensive metrics.

    This endpoint performs detailed analysis of the specified models including:
    - Parameter counts (total, attention, MLP, embedding)
    - FLOP calculations for forward pass
    - Memory requirements (parameters and activations)
    - Matrix shapes under different parallel configurations
    - Efficiency metrics and comparisons

    Args:
        request: Model analysis request with model names and configuration
        background_tasks: FastAPI background tasks for async processing

    Returns:
        ModelAnalysisResponse with detailed metrics and comparison results

    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()

        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400, detail="At least one model name must be provided"
            )

        # Prepare parallel configurations
        parallel_configs = []
        if request.parallel_configs:
            if len(request.parallel_configs) != len(request.model_names):
                raise HTTPException(
                    status_code=400,
                    detail="Number of parallel configs must match number of models",
                )
            parallel_configs = [
                config.to_parallel_config() for config in request.parallel_configs
            ]
        else:
            # Use default parallel config for all models
            default_config = (
                request.parallel_config.to_parallel_config()
                if request.parallel_config
                else ParallelConfig()
            )
            parallel_configs = [default_config] * len(request.model_names)

        # Create request ID and initialize status
        request_id = (
            f"analysis_{int(start_time.timestamp())}_{hash(str(request.model_names))}"
        )

        # Initialize analysis status
        analysis_status[request_id] = AnalysisStatus(
            request_id=request_id,
            status="running",
            progress=0.0,
            message="Starting analysis...",
            created_at=start_time,
            updated_at=start_time,
        )

        # Notify WebSocket clients about analysis start
        await manager.broadcast(
            {
                "type": "analysis_started",
                "request_id": request_id,
                "models": request.model_names,
                "timestamp": start_time.isoformat(),
            }
        )

        # Perform model comparison with progress updates
        logger.info(f"Starting analysis for models: {request.model_names}")

        try:
            # Update progress: Starting comparison
            analysis_status[request_id].progress = 0.1
            analysis_status[request_id].message = "Fetching model configurations..."
            analysis_status[request_id].updated_at = datetime.now()

            await manager.broadcast(
                {
                    "type": "progress_update",
                    "request_id": request_id,
                    "progress": 0.1,
                    "message": "Fetching model configurations...",
                }
            )

            comparison_result = comparator.compare_models(
                model_names=request.model_names,
                sequence_length=request.sequence_length,
                batch_size=request.batch_size,
                parallel_configs=parallel_configs,
                precision=request.precision,
            )

            # Update progress: Comparison completed
            analysis_status[request_id].progress = 0.8
            analysis_status[request_id].message = "Generating individual metrics..."
            analysis_status[request_id].updated_at = datetime.now()

            await manager.broadcast(
                {
                    "type": "progress_update",
                    "request_id": request_id,
                    "progress": 0.8,
                    "message": "Generating individual metrics...",
                }
            )

        except Exception as e:
            # Update status on error
            analysis_status[request_id].status = "failed"
            analysis_status[request_id].error = str(e)
            analysis_status[request_id].updated_at = datetime.now()

            await manager.broadcast(
                {
                    "type": "analysis_failed",
                    "request_id": request_id,
                    "error": str(e),
                    "timestamp": datetime.now().isoformat(),
                }
            )

            raise

        # Generate individual model metrics
        individual_results = {}
        for i, model_name in enumerate(request.model_names):
            try:
                model = model_factory.create_model(model_name)
                metrics = model.get_metrics(
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    parallel_config=parallel_configs[i],
                    kv_lens=request.kv_lens,
                )

                # If mixed precision is specified, recalculate memory requirements
                if any(
                    [
                        request.weight_dtype,
                        request.activation_dtype,
                        request.grad_dtype,
                        request.optimizer_dtype,
                        request.kv_cache_dtype,
                        request.expert_parameter_dtype,
                        request.attention_parameter_dtype,
                    ]
                ):
                    memory_breakdown = model.compute_memory_requirements(
                        sequence_length=request.sequence_length,
                        batch_size=request.batch_size,
                        dtype=request.precision.value,
                        weight_dtype=request.weight_dtype,
                        activation_dtype=request.activation_dtype,
                        grad_dtype=request.grad_dtype,
                        optimizer_dtype=request.optimizer_dtype,
                        kv_cache_dtype=request.kv_cache_dtype,
                        expert_parameter_dtype=request.expert_parameter_dtype,
                        attention_parameter_dtype=request.attention_parameter_dtype,
                        training=request.training,
                        include_kv_cache=True,
                    )

                    # Update metrics with mixed precision memory calculations
                    metrics.memory_params = memory_breakdown.get("parameters", 0)
                    metrics.memory_activations = memory_breakdown.get("activations", 0)
                    metrics.memory_total = memory_breakdown.get("total", 0)

                individual_results[model_name] = ModelMetricsModel.from_model_metrics(
                    metrics
                )
            except Exception as e:
                logger.error(f"Error getting metrics for {model_name}: {e}")
                print("Traceback:")
                traceback.print_exc()
                # Create placeholder metrics for failed models
                placeholder_metrics = ModelMetrics(
                    model_name=model_name,
                    architecture="unknown",
                    total_params=0,
                    attention_params=0,
                    mlp_params=0,
                    embedding_params=0,
                    flops_forward=0,
                    flops_per_token=0,
                    memory_params=0,
                    memory_activations=0,
                    memory_total=0,
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    parallel_config=parallel_configs[i],
                )
                individual_results[model_name] = ModelMetricsModel.from_model_metrics(
                    placeholder_metrics
                )

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        # Create response
        response = ModelAnalysisResponse(
            results=individual_results,
            comparison=(
                ComparisonResultModel.from_comparison_result(comparison_result)
                if request.include_comparison
                else None
            ),
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id,
        )

        # Update final status
        analysis_status[request_id].status = "completed"
        analysis_status[request_id].progress = 1.0
        analysis_status[request_id].message = "Analysis completed successfully"
        analysis_status[request_id].completed_at = end_time
        analysis_status[request_id].updated_at = end_time
        analysis_status[request_id].result = response

        # Notify WebSocket clients about completion
        await manager.broadcast(
            {
                "type": "analysis_completed",
                "request_id": request_id,
                "execution_time": execution_time,
                "timestamp": end_time.isoformat(),
                "models_analyzed": len(individual_results),
            }
        )

        logger.info(f"Analysis completed in {execution_time:.2f} seconds")

        return response

    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(status_code=400, detail=f"Model not supported: {str(e)}")
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(status_code=400, detail=f"Configuration error: {str(e)}")
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/models/supported", response_model=SupportedModelsResponse)
async def get_supported_models(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> SupportedModelsResponse:
    """
    Get list of supported model architectures and example models.

    Returns information about:
    - Supported model architectures (llama, deepseek, qwen, etc.)
    - Example model names for each architecture
    - Architecture-specific features and capabilities

    Note: All model names are supported by defaulting to appropriate architectures.

    Returns:
        SupportedModelsResponse with architecture information
    """
    try:
        # Get supported architectures from model factory
        architectures = model_factory.get_supported_architectures()

        # Define example models and features for each architecture type
        architecture_info = {
            "dense": {
                "examples": [
                    "meta-llama/Meta-Llama-3-8B-Instruct",
                    "meta-llama/Meta-Llama-3-70B-Instruct",
                    "Qwen/Qwen3-4B-Thinking-2507",
                ],
                "features": [
                    "Standard dense transformer architecture",
                    "All parameters active for every token",
                    "Includes Llama, Qwen, Mistral, and similar models",
                    "Grouped Query Attention (GQA) support",
                    "RMSNorm and LayerNorm normalization",
                    "Various activation functions (SwiGLU, GELU, etc.)",
                ],
                "supports_moe": False,
            },
            "moe": {
                "examples": [
                    "deepseek-ai/DeepSeek-V3",
                    "moonshotai/Kimi-K2-Instruct",
                    "Qwen/Qwen3-235B-A22B",
                    "openai/gpt-oss-120b",
                    # "deepseek-ai/DeepSeek-v2-lite",
                    # "Qwen/Qwen3-30B-A3B-Thinking-2507",
                ],
                "features": [
                    "Mixture of Experts architecture",
                    "Sparse activation - only subset of parameters active per token",
                    "Includes DeepSeek, Mixtral, and other MoE models",
                    "Expert routing and load balancing",
                    "Shared and routed expert configurations",
                    "Higher parameter efficiency",
                ],
                "supports_moe": True,
            },
        }

        # Build response with registered architectures
        from .models import ArchitectureInfo

        supported_info = {}
        for arch in architectures:
            if arch in architecture_info:
                info = architecture_info[arch]
                supported_info[arch] = ArchitectureInfo(
                    examples=info["examples"],
                    features=info["features"],
                    supports_moe=info["supports_moe"],
                )
            else:
                # Handle any custom registered architectures
                supported_info[arch] = ArchitectureInfo(
                    examples=[f"Custom {arch} models"],
                    features=["Custom architecture"],
                    supports_moe=False,
                )

        return SupportedModelsResponse(
            architectures=list(supported_info.keys()),
            architecture_info=supported_info,
            total_architectures=len(supported_info),
        )

    except Exception as e:
        logger.error(f"Error getting supported models: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving supported models: {str(e)}"
        )


@app.get("/api/models/validate/{model_name}")
async def validate_model(
    model_name: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Validate if a model is supported and can be analyzed.

    Args:
        model_name: Name of the model to validate

    Returns:
        Dictionary with validation results and model information
    """
    try:
        # Try to create model instance to validate
        model = model_factory.create_model(model_name)

        # Get basic model information
        config = model.config
        architecture = getattr(config, "model_type", "unknown")

        return {
            "valid": True,
            "model_name": model_name,
            "architecture": architecture,
            "supported": True,
            "config_available": config is not None,
            "basic_info": {
                "hidden_size": getattr(config, "hidden_size", None),
                "num_layers": getattr(config, "num_hidden_layers", None),
                "num_heads": getattr(config, "num_attention_heads", None),
                "vocab_size": getattr(config, "vocab_size", None),
            },
        }

    except ModelNotSupportedError as e:
        return {
            "valid": False,
            "model_name": model_name,
            "supported": False,
            "error": str(e),
            "error_type": "unsupported_architecture",
        }
    except ConfigurationError as e:
        return {
            "valid": False,
            "model_name": model_name,
            "supported": True,
            "config_available": False,
            "error": str(e),
            "error_type": "configuration_error",
        }
    except Exception as e:
        return {
            "valid": False,
            "model_name": model_name,
            "error": str(e),
            "error_type": "unknown_error",
        }


@app.get("/api/analysis/status/{request_id}")
async def get_analysis_status(
    request_id: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> AnalysisStatus:
    """
    Get the status of a background analysis task.

    Args:
        request_id: ID of the analysis request

    Returns:
        AnalysisStatus with current status information
    """
    if request_id not in analysis_status:
        raise HTTPException(
            status_code=404, detail=f"Analysis request {request_id} not found"
        )

    return analysis_status[request_id]


@app.get("/api/analysis/list")
async def list_analysis_requests(
    limit: int = 50,
    status_filter: Optional[str] = None,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    List recent analysis requests with optional filtering.

    Args:
        limit: Maximum number of requests to return
        status_filter: Filter by status (pending, running, completed, failed)

    Returns:
        Dictionary with analysis request list and metadata
    """
    # Filter and sort analysis requests
    filtered_requests = []

    for request_id, status in analysis_status.items():
        if status_filter and status.status != status_filter:
            continue
        filtered_requests.append(status)

    # Sort by creation time (newest first)
    filtered_requests.sort(key=lambda x: x.created_at, reverse=True)

    # Apply limit
    limited_requests = filtered_requests[:limit]

    return {
        "requests": limited_requests,
        "total_count": len(filtered_requests),
        "returned_count": len(limited_requests),
        "status_counts": {
            "pending": sum(
                1 for s in analysis_status.values() if s.status == "pending"
            ),
            "running": sum(
                1 for s in analysis_status.values() if s.status == "running"
            ),
            "completed": sum(
                1 for s in analysis_status.values() if s.status == "completed"
            ),
            "failed": sum(1 for s in analysis_status.values() if s.status == "failed"),
        },
    }


@app.delete("/api/analysis/{request_id}")
async def cancel_analysis(
    request_id: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, str]:
    """
    Cancel a running analysis request.

    Args:
        request_id: ID of the analysis request to cancel

    Returns:
        Cancellation status message
    """
    if request_id not in analysis_status:
        raise HTTPException(
            status_code=404, detail=f"Analysis request {request_id} not found"
        )

    status = analysis_status[request_id]

    if status.status in ["completed", "failed", "cancelled"]:
        raise HTTPException(
            status_code=400, detail=f"Cannot cancel analysis in status: {status.status}"
        )

    # Update status to cancelled
    status.status = "cancelled"
    status.updated_at = datetime.now()
    status.error = "Analysis cancelled by user"

    # Notify WebSocket clients
    await manager.broadcast(
        {
            "type": "analysis_cancelled",
            "request_id": request_id,
            "timestamp": datetime.now().isoformat(),
        }
    )

    return {"message": f"Analysis request {request_id} cancelled successfully"}


@app.get("/api/stats")
async def get_api_stats(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get API usage statistics and system information.

    Returns:
        Dictionary with API statistics and system info
    """
    # Calculate uptime
    import sys

    import psutil

    current_time = datetime.now()

    # Analysis statistics
    total_analyses = len(analysis_status)
    completed_analyses = sum(
        1 for s in analysis_status.values() if s.status == "completed"
    )
    failed_analyses = sum(1 for s in analysis_status.values() if s.status == "failed")

    # Recent activity (last 24 hours)
    recent_cutoff = current_time - timedelta(hours=24)
    recent_analyses = sum(
        1 for s in analysis_status.values() if s.created_at >= recent_cutoff
    )

    # System information
    memory_info = psutil.virtual_memory()
    cpu_percent = psutil.cpu_percent(interval=1)

    return {
        "api_info": {
            "version": "1.0.0",
            "uptime_seconds": time.time() - psutil.Process().create_time(),
            "current_time": current_time.isoformat(),
        },
        "analysis_stats": {
            "total_analyses": total_analyses,
            "completed_analyses": completed_analyses,
            "failed_analyses": failed_analyses,
            "success_rate": (
                completed_analyses / max(total_analyses, 1) * 100
                if total_analyses > 0
                else 0
            ),
            "recent_24h": recent_analyses,
        },
        "system_info": {
            "python_version": sys.version,
            "memory_usage_percent": memory_info.percent,
            "memory_available_gb": memory_info.available / (1024**3),
            "cpu_usage_percent": cpu_percent,
        },
        "websocket_info": {
            "active_connections": len(manager.active_connections),
            "connection_ids": list(manager.active_connections.keys()),
        },
        "rate_limiting": {
            "requests_per_window": RATE_LIMIT_REQUESTS,
            "window_seconds": RATE_LIMIT_WINDOW,
            "active_clients": len(rate_limit_storage),
        },
    }


@app.post("/api/export")
async def export_results(
    request: Dict[str, Any],
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
):
    """
    Export analysis results in various formats.

    Args:
        request: Export request containing comparison data and format

    Returns:
        File response with exported data
    """
    try:
        comparison_data = request.get("comparison")
        export_format = request.get("format", "json")
        request_id = request.get("request_id")

        if not comparison_data:
            raise HTTPException(
                status_code=400, detail="Comparison data is required for export"
            )

        # Create comparison result object
        comparison = ComparisonResult(
            models=comparison_data["models"],
            metrics=comparison_data["metrics"],
            parallel_configs=[],  # Will be populated if needed
            sequence_length=comparison_data.get("sequence_length", 2048),
            batch_size=comparison_data.get("batch_size", 1),
            timestamp=datetime.now(),
            metadata=comparison_data.get("metadata", {}),
        )

        # Generate filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        if export_format == "json":
            # Export as JSON
            import json
            import tempfile

            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".json", delete=False
            ) as f:
                json.dump(comparison_data, f, indent=2, default=str)
                temp_path = f.name

            return FileResponse(
                temp_path,
                media_type="application/json",
                filename=f"model_comparison_{timestamp}.json",
                headers={
                    "Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.json"
                },
            )

        elif export_format == "csv":
            # Export as CSV
            import tempfile

            import pandas as pd

            # Convert metrics to DataFrame
            df_data = []
            for metric_name, values in comparison_data["metrics"].items():
                for i, model in enumerate(comparison_data["models"]):
                    if i < len(values):
                        df_data.append(
                            {"Model": model, "Metric": metric_name, "Value": values[i]}
                        )

            df = pd.DataFrame(df_data)

            with tempfile.NamedTemporaryFile(
                mode="w", suffix=".csv", delete=False
            ) as f:
                df.to_csv(f.name, index=False)
                temp_path = f.name

            return FileResponse(
                temp_path,
                media_type="text/csv",
                filename=f"model_comparison_{timestamp}.csv",
                headers={
                    "Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.csv"
                },
            )

        elif export_format == "excel":
            # Export as Excel
            import tempfile

            import pandas as pd

            with tempfile.NamedTemporaryFile(suffix=".xlsx", delete=False) as f:
                temp_path = f.name

            # Create Excel writer
            with pd.ExcelWriter(temp_path, engine="openpyxl") as writer:
                # Summary sheet
                summary_data = []
                for i, model in enumerate(comparison_data["models"]):
                    row = {"Model": model}
                    for metric_name, values in comparison_data["metrics"].items():
                        if i < len(values):
                            row[metric_name] = values[i]
                    summary_data.append(row)

                summary_df = pd.DataFrame(summary_data)
                summary_df.to_excel(writer, sheet_name="Summary", index=False)

                # Individual metric sheets
                for metric_name, values in comparison_data["metrics"].items():
                    metric_df = pd.DataFrame(
                        {
                            "Model": comparison_data["models"][: len(values)],
                            metric_name: values,
                        }
                    )
                    # Clean sheet name for Excel
                    sheet_name = metric_name.replace("_", " ").title()[:31]
                    metric_df.to_excel(writer, sheet_name=sheet_name, index=False)

            return FileResponse(
                temp_path,
                media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                filename=f"model_comparison_{timestamp}.xlsx",
                headers={
                    "Content-Disposition": f"attachment; filename=model_comparison_{timestamp}.xlsx"
                },
            )

        else:
            raise HTTPException(
                status_code=400, detail=f"Unsupported export format: {export_format}"
            )

    except Exception as e:
        logger.error(f"Export failed: {e}")
        raise HTTPException(status_code=500, detail=f"Export failed: {str(e)}")


# Memory Analysis Endpoints


@app.post("/api/memory/analyze", response_model=MemoryAnalysisResponse)
async def analyze_memory_requirements(
    request: MemoryAnalysisRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> MemoryAnalysisResponse:
    """
    Analyze memory requirements with dtype and sequence length variations.

    This endpoint provides detailed memory analysis including:
    - Parameter memory usage
    - KV cache memory with configurable dtype
    - Activation memory requirements
    - Attention mechanism detection (MHA, GQA, MLA)
    - Total memory breakdown

    Args:
        request: Memory analysis request with models and configuration

    Returns:
        MemoryAnalysisResponse with detailed memory breakdown for each model

    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()

        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400, detail="At least one model name must be provided"
            )

        # Create request ID
        request_id = f"memory_analysis_{int(start_time.timestamp())}_{hash(str(request.model_names))}"

        logger.info(f"Starting memory analysis for models: {request.model_names}")

        model_results = {}

        for model_name in request.model_names:
            try:
                # Create model instance
                model = model_factory.create_model(model_name)

                # Get memory breakdown with mixed precision support
                memory_breakdown = model.compute_memory_requirements(
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    dtype=request.dtype,
                    weight_dtype=request.weight_dtype,
                    activation_dtype=request.activation_dtype,
                    grad_dtype=request.grad_dtype,
                    optimizer_dtype=request.optimizer_dtype,
                    kv_cache_dtype=request.kv_cache_dtype,
                    expert_parameter_dtype=request.expert_parameter_dtype,
                    attention_parameter_dtype=request.attention_parameter_dtype,
                    training=request.training,
                    include_kv_cache=request.include_kv_cache,
                )

                # Get attention mechanism type
                attention_mechanism = model.get_attention_mechanism_type()

                # Create memory breakdown response
                model_results[model_name] = MemoryBreakdown(
                    parameters=memory_breakdown.get("parameters", 0),
                    kv_cache=memory_breakdown.get("kv_cache", 0),
                    activations=memory_breakdown.get("activations", 0),
                    gradients=(
                        memory_breakdown.get("gradients") if request.training else None
                    ),
                    optimizer_states=(
                        memory_breakdown.get("optimizer_states")
                        if request.training
                        else None
                    ),
                    total=memory_breakdown.get("total", 0),
                    dtype=memory_breakdown.get("dtype", request.dtype),
                    dtypes=memory_breakdown.get("dtypes"),
                    attention_mechanism=attention_mechanism,
                    training=request.training,
                )

            except Exception as e:
                logger.error(f"Error analyzing memory for {model_name}: {e}")
                # Create placeholder breakdown for failed models
                model_results[model_name] = MemoryBreakdown(
                    parameters=0,
                    kv_cache=0,
                    activations=0,
                    gradients=0 if request.training else None,
                    optimizer_states=0 if request.training else None,
                    total=0,
                    dtype=request.dtype,
                    dtypes=None,
                    attention_mechanism="Unknown",
                    training=request.training,
                )

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        response = MemoryAnalysisResponse(
            model_results=model_results,
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id,
        )

        logger.info(f"Memory analysis completed in {execution_time:.2f} seconds")
        return response

    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(status_code=400, detail=f"Model not supported: {str(e)}")
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(status_code=400, detail=f"Configuration error: {str(e)}")
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during memory analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.post("/api/memory/kv-growth", response_model=MemoryAnalysisResponse)
async def analyze_kv_growth(
    request: KVGrowthAnalysisRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> MemoryAnalysisResponse:
    """
    Analyze KV cache memory growth across sequence lengths.

    This endpoint analyzes how KV cache memory grows with sequence length,
    providing insights into the memory efficiency of different attention mechanisms.

    Args:
        request: KV growth analysis request with sequence length range

    Returns:
        MemoryAnalysisResponse with KV growth data for each model

    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()

        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400, detail="At least one model name must be provided"
            )

        if request.max_sequence_length <= request.min_sequence_length:
            raise HTTPException(
                status_code=400,
                detail="max_sequence_length must be greater than min_sequence_length",
            )

        # Create request ID
        request_id = (
            f"kv_growth_{int(start_time.timestamp())}_{hash(str(request.model_names))}"
        )

        logger.info(f"Starting KV growth analysis for models: {request.model_names}")

        # Generate sequence length range
        sequence_lengths = list(
            range(
                request.min_sequence_length,
                request.max_sequence_length + 1,
                request.sequence_length_step,
            )
        )

        model_results = {}
        kv_growth_data = {}

        for model_name in request.model_names:
            try:
                # Create model instance
                model = model_factory.create_model(model_name)

                # Get model configuration for memory calculations
                config_dict = (
                    model._parsed_config if hasattr(model, "_parsed_config") else {}
                )
                total_params = model.get_total_params()

                # Analyze memory growth across sequence lengths
                from ..metrics.memory_calculator import MemoryCalculator

                growth_analysis = (
                    MemoryCalculator.analyze_memory_growth_by_sequence_length(
                        config_dict,
                        total_params,
                        sequence_lengths,
                        request.batch_size,
                        request.dtype,
                    )
                )

                # Convert to KVGrowthPoint objects
                growth_points = []
                for data_point in growth_analysis["memory_data"]:
                    memory_bytes = data_point["kv_cache_memory"]
                    memory_human = MemoryCalculator._format_memory(memory_bytes)

                    growth_points.append(
                        KVGrowthPoint(
                            sequence_length=data_point["sequence_length"],
                            memory_bytes=memory_bytes,
                            memory_human=memory_human,
                        )
                    )

                kv_growth_data[model_name] = growth_points

                # Create summary memory breakdown for the maximum sequence length
                max_seq_memory = (
                    growth_analysis["memory_data"][-1]
                    if growth_analysis["memory_data"]
                    else {}
                )
                attention_mechanism = growth_analysis.get(
                    "attention_mechanism", {}
                ).get("type", "Unknown")

                model_results[model_name] = MemoryBreakdown(
                    parameters=max_seq_memory.get("parameter_memory", 0),
                    kv_cache=max_seq_memory.get("kv_cache_memory", 0),
                    activations=max_seq_memory.get("activation_memory", 0),
                    total=max_seq_memory.get("total_memory", 0),
                    dtype=request.dtype,
                    attention_mechanism=attention_mechanism,
                )

            except Exception as e:
                logger.error(f"Error analyzing KV growth for {model_name}: {e}")
                # Create placeholder data for failed models
                model_results[model_name] = MemoryBreakdown(
                    parameters=0,
                    kv_cache=0,
                    activations=0,
                    total=0,
                    dtype=request.dtype,
                    attention_mechanism="Unknown",
                )
                kv_growth_data[model_name] = []

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        response = MemoryAnalysisResponse(
            model_results=model_results,
            kv_growth_data=kv_growth_data,
            execution_time=execution_time,
            timestamp=end_time,
            request_id=request_id,
        )

        logger.info(f"KV growth analysis completed in {execution_time:.2f} seconds")
        return response

    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(status_code=400, detail=f"Model not supported: {str(e)}")
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(status_code=400, detail=f"Configuration error: {str(e)}")
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during KV growth analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/memory/dtypes")
async def get_supported_dtypes(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get list of supported data types for memory calculations.

    Returns information about supported dtypes including:
    - Available data types (fp16, bf16, fp32, int8, fp8, fp4)
    - Bytes per element for each dtype
    - Recommended use cases

    Returns:
        Dictionary with supported dtype information
    """
    try:
        # Define supported dtypes with their properties
        supported_dtypes = {
            "fp32": {
                "bytes_per_element": 4,
                "description": "32-bit floating point",
                "use_case": "Highest precision, largest memory usage",
                "components": [
                    "weights",
                    "activations",
                    "gradients",
                    "optimizer_states",
                    "kv_cache",
                ],
            },
            "fp16": {
                "bytes_per_element": 2,
                "description": "16-bit floating point",
                "use_case": "Good balance of precision and memory efficiency",
                "components": [
                    "weights",
                    "activations",
                    "gradients",
                    "optimizer_states",
                    "kv_cache",
                ],
            },
            "bf16": {
                "bytes_per_element": 2,
                "description": "16-bit brain floating point",
                "use_case": "Better numerical stability than fp16",
                "components": [
                    "weights",
                    "activations",
                    "gradients",
                    "optimizer_states",
                    "kv_cache",
                ],
            },
            "int8": {
                "bytes_per_element": 1,
                "description": "8-bit integer",
                "use_case": "Quantized models, significant memory savings",
                "components": ["weights", "kv_cache"],
            },
            "fp8": {
                "bytes_per_element": 1,
                "description": "8-bit floating point",
                "use_case": "Advanced quantization with floating point representation",
                "components": ["weights", "kv_cache", "expert_parameters"],
            },
            "fp4": {
                "bytes_per_element": 0.5,
                "description": "4-bit floating point (packed)",
                "use_case": "Extreme quantization for maximum memory savings",
                "components": ["weights", "expert_parameters"],
            },
        }

        # Add memory multipliers relative to fp16
        for dtype, info in supported_dtypes.items():
            info["memory_multiplier"] = info["bytes_per_element"] / 2.0

        return {
            "supported_dtypes": list(supported_dtypes.keys()),
            "default_dtype": "fp16",
            "dtype_info": supported_dtypes,
            "total_supported": len(supported_dtypes),
            "mixed_precision_defaults": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "bf16",
                "expert_parameter_dtype": "fp8",
                "attention_parameter_dtype": "bf16",
                "grad_dtype": "fp16",
                "optimizer_dtype": "fp32",
            },
        }

    except Exception as e:
        logger.error(f"Error getting supported dtypes: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving supported dtypes: {str(e)}"
        )


@app.post("/api/memory/mixed-precision", response_model=Dict[str, Any])
async def analyze_mixed_precision_memory(
    request: MemoryAnalysisRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Analyze memory requirements with detailed mixed precision breakdown.

    This endpoint provides enhanced memory analysis with mixed precision support:
    - Detailed breakdown by component and precision type
    - Memory efficiency metrics and savings calculations
    - Precision configuration validation
    - Component-specific memory usage

    Args:
        request: Memory analysis request with mixed precision configuration

    Returns:
        Dictionary with enhanced mixed precision memory analysis

    Raises:
        HTTPException: If analysis fails or models are not supported
    """
    try:
        start_time = datetime.now()

        # Validate request
        if not request.model_names:
            raise HTTPException(
                status_code=400, detail="At least one model name must be provided"
            )

        # Create request ID
        request_id = f"mixed_precision_{int(start_time.timestamp())}_{hash(str(request.model_names))}"

        logger.info(
            f"Starting mixed precision memory analysis for models: {request.model_names}"
        )

        model_results = {}

        for model_name in request.model_names:
            try:
                # Create model instance
                model = model_factory.create_model(model_name)

                # Get memory breakdown with mixed precision support
                memory_breakdown = model.compute_memory_requirements(
                    sequence_length=request.sequence_length,
                    batch_size=request.batch_size,
                    dtype=request.dtype,
                    weight_dtype=request.weight_dtype,
                    activation_dtype=request.activation_dtype,
                    grad_dtype=request.grad_dtype,
                    optimizer_dtype=request.optimizer_dtype,
                    kv_cache_dtype=request.kv_cache_dtype,
                    expert_parameter_dtype=request.expert_parameter_dtype,
                    attention_parameter_dtype=request.attention_parameter_dtype,
                    training=request.training,
                    include_kv_cache=request.include_kv_cache,
                )

                # Get attention mechanism type
                attention_mechanism = model.get_attention_mechanism_type()

                # Create enhanced mixed precision breakdown
                precision_config = {
                    "weight_dtype": request.weight_dtype or request.dtype,
                    "activation_dtype": request.activation_dtype or request.dtype,
                    "kv_cache_dtype": request.kv_cache_dtype or request.dtype,
                    "grad_dtype": request.grad_dtype if request.training else None,
                    "optimizer_dtype": (
                        request.optimizer_dtype if request.training else None
                    ),
                    "expert_parameter_dtype": request.expert_parameter_dtype,
                    "attention_parameter_dtype": request.attention_parameter_dtype,
                }

                # Calculate efficiency metrics
                baseline_memory = memory_breakdown.get(
                    "total", 0
                )  # This would be calculated with fp16
                efficiency_metrics = {
                    "memory_savings_percent": 0.0,  # Would be calculated based on baseline
                    "compression_ratio": 1.0,
                    "precision_diversity": len(
                        set(filter(None, precision_config.values()))
                    ),
                }

                model_results[model_name] = MixedPrecisionMemoryBreakdown(
                    total=memory_breakdown.get("total", 0),
                    by_component={
                        "parameters": memory_breakdown.get("parameters", 0),
                        "activations": memory_breakdown.get("activations", 0),
                        "kv_cache": memory_breakdown.get("kv_cache", 0),
                        "gradients": (
                            memory_breakdown.get("gradients", 0)
                            if request.training
                            else 0
                        ),
                        "optimizer_states": (
                            memory_breakdown.get("optimizer_states", 0)
                            if request.training
                            else 0
                        ),
                    },
                    by_precision=memory_breakdown.get("by_precision", {}),
                    precision_config=precision_config,
                    efficiency_metrics=efficiency_metrics,
                    attention_mechanism=attention_mechanism,
                    training=request.training,
                )

            except Exception as e:
                logger.error(
                    f"Error analyzing mixed precision memory for {model_name}: {e}"
                )
                # Create placeholder breakdown for failed models
                model_results[model_name] = MixedPrecisionMemoryBreakdown(
                    total=0,
                    by_component={"parameters": 0, "activations": 0, "kv_cache": 0},
                    by_precision={},
                    precision_config={},
                    efficiency_metrics={
                        "memory_savings_percent": 0.0,
                        "compression_ratio": 1.0,
                        "precision_diversity": 0,
                    },
                    attention_mechanism="Unknown",
                    training=request.training,
                )

        end_time = datetime.now()
        execution_time = (end_time - start_time).total_seconds()

        response = {
            "model_results": {
                name: result.dict() for name, result in model_results.items()
            },
            "execution_time": execution_time,
            "timestamp": end_time.isoformat(),
            "request_id": request_id,
            "analysis_type": "mixed_precision_memory",
        }

        logger.info(
            f"Mixed precision memory analysis completed in {execution_time:.2f} seconds"
        )
        return response

    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(status_code=400, detail=f"Model not supported: {str(e)}")
    except ConfigurationError as e:
        logger.error(f"Configuration error: {e}")
        raise HTTPException(status_code=400, detail=f"Configuration error: {str(e)}")
    except ValueError as e:
        logger.error(f"Validation error: {e}")
        raise HTTPException(status_code=400, detail=f"Validation error: {str(e)}")
    except Exception as e:
        logger.error(f"Unexpected error during mixed precision analysis: {e}")
        logger.error(traceback.format_exc())
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@app.get("/api/memory/precision-recommendations")
async def get_precision_recommendations(
    model_name: str,
    use_case: str = "inference",  # inference, training, memory_optimized, quality_optimized
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get recommended precision configurations for different use cases.

    This endpoint provides optimized precision configurations based on:
    - Model architecture and size
    - Use case requirements (inference vs training)
    - Memory vs quality trade-offs

    Args:
        model_name: Name of the model to get recommendations for
        use_case: Target use case (inference, training, memory_optimized, quality_optimized)

    Returns:
        Dictionary with recommended precision configurations

    Raises:
        HTTPException: If model is not supported or recommendations cannot be generated
    """
    try:
        # Validate use case
        valid_use_cases = [
            "inference",
            "training",
            "memory_optimized",
            "quality_optimized",
        ]
        if use_case not in valid_use_cases:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid use case: {use_case}. Valid options: {valid_use_cases}",
            )

        # Create model instance to get architecture info
        model = model_factory.create_model(model_name)
        config = model.config
        architecture = getattr(config, "model_type", "unknown")

        # Determine if it's a MoE model
        is_moe = hasattr(model, "num_experts") or "moe" in architecture.lower()

        # Base recommendations by use case
        recommendations = {
            "inference": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "fp8" if is_moe else "bf16",
                "expert_parameter_dtype": "fp8" if is_moe else None,
                "attention_parameter_dtype": "bf16",
                "description": "Balanced precision for inference workloads",
            },
            "training": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "bf16",
                "grad_dtype": "fp16",
                "optimizer_dtype": "fp32",
                "expert_parameter_dtype": "bf16" if is_moe else None,
                "attention_parameter_dtype": "bf16",
                "description": "Stable precision configuration for training",
            },
            "memory_optimized": {
                "weight_dtype": "int8",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "fp8",
                "expert_parameter_dtype": "fp4" if is_moe else None,
                "attention_parameter_dtype": "fp8",
                "description": "Aggressive quantization for maximum memory savings",
            },
            "quality_optimized": {
                "weight_dtype": "bf16",
                "activation_dtype": "bf16",
                "kv_cache_dtype": "bf16",
                "expert_parameter_dtype": "bf16" if is_moe else None,
                "attention_parameter_dtype": "bf16",
                "description": "High precision for maximum model quality",
            },
        }

        # Get the specific recommendation
        recommendation = recommendations[use_case]

        # Add model-specific adjustments
        model_info = {
            "model_name": model_name,
            "architecture": architecture,
            "is_moe": is_moe,
            "estimated_params": (
                getattr(model, "total_params", 0)
                if hasattr(model, "total_params")
                else 0
            ),
        }

        # Calculate estimated memory savings
        baseline_config = {
            k: "fp16" for k in recommendation.keys() if k not in ["description"]
        }
        estimated_savings = 0.0  # Would be calculated based on precision differences

        if use_case == "memory_optimized":
            estimated_savings = 40.0  # Estimated 40% savings
        elif use_case == "inference" and is_moe:
            estimated_savings = 20.0  # Estimated 20% savings for MoE inference

        return {
            "model_info": model_info,
            "use_case": use_case,
            "recommended_config": recommendation,
            "estimated_memory_savings_percent": estimated_savings,
            "alternative_configs": {
                name: config
                for name, config in recommendations.items()
                if name != use_case
            },
            "notes": [
                "Recommendations are based on general best practices",
                "Actual performance may vary depending on hardware and implementation",
                "Consider testing different configurations for your specific use case",
            ],
        }

    except ModelNotSupportedError as e:
        logger.error(f"Model not supported: {e}")
        raise HTTPException(status_code=400, detail=f"Model not supported: {str(e)}")
    except Exception as e:
        logger.error(f"Error generating precision recommendations: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating recommendations: {str(e)}"
        )


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler with detailed error information."""
    client_ip = get_client_ip(request)

    # Log the error with context
    logger.warning(
        f"HTTP {exc.status_code} error from {client_ip}: {exc.detail} "
        f"[{request.method} {request.url}]"
    )

    # Create detailed error response
    error_response = ErrorResponse(
        error=exc.detail,
        status_code=exc.status_code,
        timestamp=datetime.now(),
        request_id=f"error_{int(time.time())}_{hash(str(request.url))}",
    )

    # Add rate limit specific headers
    if exc.status_code == 429:
        headers = {"Retry-After": str(RATE_LIMIT_WINDOW)}
    else:
        headers = {}

    return JSONResponse(
        status_code=exc.status_code,
        content=error_response.model_dump(),
        headers=headers,
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler for unhandled errors with comprehensive logging."""
    client_ip = get_client_ip(request)
    error_id = f"error_{int(time.time())}_{hash(str(exc))}"

    # Log detailed error information
    logger.error(
        f"Unhandled exception {error_id} from {client_ip}: {type(exc).__name__}: {exc} "
        f"[{request.method} {request.url}]"
    )
    logger.error(f"Full traceback for {error_id}:\n{traceback.format_exc()}")

    # Determine if this is a known error type
    error_message = "Internal server error"
    details = None

    if isinstance(exc, (ModelNotSupportedError, ConfigurationError)):
        error_message = f"Model error: {str(exc)}"
        details = type(exc).__name__
    elif isinstance(exc, ValueError):
        error_message = f"Validation error: {str(exc)}"
        details = "ValueError"
    elif isinstance(exc, ConnectionError):
        error_message = "External service connection error"
        details = "ConnectionError"
    elif isinstance(exc, TimeoutError):
        error_message = "Request timeout"
        details = "TimeoutError"
    else:
        # For unknown errors, include details only in debug mode
        if os.getenv("DEBUG", "false").lower() == "true":
            details = f"{type(exc).__name__}: {str(exc)}"

    error_response = ErrorResponse(
        error=error_message,
        status_code=500,
        timestamp=datetime.now(),
        details=details,
        request_id=error_id,
    )

    return JSONResponse(status_code=500, content=error_response.model_dump())


# Add middleware for request logging
@app.middleware("http")
async def log_requests(request: Request, call_next):
    """Middleware to log all requests with timing information."""
    start_time = time.time()
    client_ip = get_client_ip(request)

    # Log request start
    logger.info(f"Request started: {request.method} {request.url} from {client_ip}")

    try:
        response = await call_next(request)
        process_time = time.time() - start_time

        # Log successful response
        logger.info(
            f"Request completed: {request.method} {request.url} "
            f"-> {response.status_code} ({process_time:.3f}s)"
        )

        # Add timing header
        response.headers["X-Process-Time"] = str(process_time)
        return response

    except Exception as e:
        process_time = time.time() - start_time
        logger.error(
            f"Request failed: {request.method} {request.url} "
            f"-> {type(e).__name__}: {e} ({process_time:.3f}s)"
        )
        raise


from ..hardware.models import WorkloadProfile
from ..hardware.roofline_service import RooflineService

# Hardware API endpoints
from ..hardware.service import HardwareService
from ..hardware.timing_service import OperatorTimingService
from .models import BottleneckAnalysisModel  # Roofline models; Timing models
from .models import (
    ComparisonPlotDataModel,
    HardwareListResponse,
    HardwareRecommendationModel,
    HardwareSpecModel,
    HardwareTypeEnum,
    HardwareValidationRequest,
    HardwareValidationResponse,
    KneePointModel,
    OperatorConfigModel,
    OperatorTimingModel,
    RooflineCompareRequest,
    RooflineDataModel,
    RooflineGenerateRequest,
    RooflinePlotDataModel,
    RooflinePlotOperatorsRequest,
    TimingAnalyzeRequest,
    TimingCompareHardwareRequest,
    TimingComparisonModel,
    ValidationResultModel,
    WorkloadProfileModel,
)

# Initialize hardware services
hardware_service = HardwareService()
roofline_service = RooflineService()
timing_service = OperatorTimingService()


@app.get("/api/hardware/list", response_model=HardwareListResponse)
async def get_available_hardware(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> HardwareListResponse:
    """
    Get list of all available hardware devices.

    Returns information about all supported GPU and NPU devices including:
    - Hardware specifications (memory, performance, etc.)
    - Supported precisions and capabilities
    - Physical characteristics and power requirements

    Returns:
        HardwareListResponse with lists of GPU and NPU devices
    """
    try:
        hardware_dict = hardware_service.get_available_hardware()

        # Convert HardwareSpec objects to Pydantic models
        gpu_models = []
        for gpu_spec in hardware_dict.get("gpu", []):
            gpu_model = HardwareSpecModel(
                id=gpu_spec.id,
                name=gpu_spec.name,
                type=HardwareTypeEnum.GPU,
                architecture=gpu_spec.architecture,
                form_factor=gpu_spec.form_factor,
                year=gpu_spec.year,
                memory_size_gb=gpu_spec.memory_size_gb,
                memory_type=gpu_spec.memory_type,
                memory_bandwidth_gbps=gpu_spec.memory_bandwidth_gbps,
                l2_cache_mb=gpu_spec.l2_cache_mb,
                peak_flops=gpu_spec.peak_flops,
                tensor_performance=gpu_spec.tensor_performance,
                vector_performance=gpu_spec.vector_performance,
                tdp_watts=gpu_spec.tdp_watts,
                manufacturing_process=gpu_spec.manufacturing_process,
                interconnect=gpu_spec.interconnect,
                supported_precisions=gpu_spec.supported_precisions,
                tensor_cores=gpu_spec.tensor_cores,
            )
            gpu_models.append(gpu_model)

        npu_models = []
        for npu_spec in hardware_dict.get("npu", []):
            npu_model = HardwareSpecModel(
                id=npu_spec.id,
                name=npu_spec.name,
                type=HardwareTypeEnum.NPU,
                architecture=npu_spec.architecture,
                form_factor=npu_spec.form_factor,
                year=npu_spec.year,
                memory_size_gb=npu_spec.memory_size_gb,
                memory_type=npu_spec.memory_type,
                memory_bandwidth_gbps=npu_spec.memory_bandwidth_gbps,
                l2_cache_mb=npu_spec.l2_cache_mb,
                peak_flops=npu_spec.peak_flops,
                tensor_performance=npu_spec.tensor_performance,
                vector_performance=npu_spec.vector_performance,
                tdp_watts=npu_spec.tdp_watts,
                manufacturing_process=npu_spec.manufacturing_process,
                interconnect=npu_spec.interconnect,
                supported_precisions=npu_spec.supported_precisions,
                tensor_cores=npu_spec.tensor_cores,
            )
            npu_models.append(npu_model)

        total_count = len(gpu_models) + len(npu_models)

        return HardwareListResponse(
            gpu=gpu_models, npu=npu_models, total_count=total_count
        )

    except Exception as e:
        logger.error(f"Error getting available hardware: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error retrieving hardware list: {str(e)}"
        )


@app.get("/api/hardware/{hardware_id}/specs", response_model=HardwareSpecModel)
async def get_hardware_specs(
    hardware_id: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> HardwareSpecModel:
    """
    Get detailed specifications for a specific hardware device.

    Args:
        hardware_id: Unique identifier for the hardware device

    Returns:
        HardwareSpecModel with detailed hardware specifications

    Raises:
        HTTPException: If hardware device is not found
    """
    try:
        hardware_spec = hardware_service.get_hardware_specs(hardware_id)

        if hardware_spec is None:
            raise HTTPException(
                status_code=404, detail=f"Hardware device '{hardware_id}' not found"
            )

        # Convert to Pydantic model
        hardware_model = HardwareSpecModel(
            id=hardware_spec.id,
            name=hardware_spec.name,
            type=(
                HardwareTypeEnum.GPU
                if hardware_spec.type.value == "gpu"
                else HardwareTypeEnum.NPU
            ),
            architecture=hardware_spec.architecture,
            form_factor=hardware_spec.form_factor,
            year=hardware_spec.year,
            memory_size_gb=hardware_spec.memory_size_gb,
            memory_type=hardware_spec.memory_type,
            memory_bandwidth_gbps=hardware_spec.memory_bandwidth_gbps,
            l2_cache_mb=hardware_spec.l2_cache_mb,
            peak_flops=hardware_spec.peak_flops,
            tensor_performance=hardware_spec.tensor_performance,
            vector_performance=hardware_spec.vector_performance,
            tdp_watts=hardware_spec.tdp_watts,
            manufacturing_process=hardware_spec.manufacturing_process,
            interconnect=hardware_spec.interconnect,
            supported_precisions=hardware_spec.supported_precisions,
            tensor_cores=hardware_spec.tensor_cores,
        )

        return hardware_model

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting hardware specs for {hardware_id}: {e}")
        raise HTTPException(
            status_code=500,
            detail=f"Error retrieving hardware specifications: {str(e)}",
        )


@app.post("/api/hardware/validate", response_model=HardwareValidationResponse)
async def validate_hardware_compatibility(
    request: HardwareValidationRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> HardwareValidationResponse:
    """
    Validate hardware compatibility with given operators.

    Checks if the specified hardware can efficiently run the given operators,
    including memory requirements, precision support, and performance characteristics.

    Args:
        request: Hardware validation request with hardware ID and operators

    Returns:
        HardwareValidationResponse with validation results and recommendations
    """
    try:
        # Create mock operators from request data
        # In a real implementation, this would convert the operator configs to actual operator objects
        mock_operators = []
        for op_config in request.operators:
            # Create a simple mock operator class
            class MockOperator:
                def __init__(self, config):
                    self.operator_type = config.get("type", "unknown")
                    self.precision = config.get("precision", "fp32")
                    self.parameters = config.get("parameters", 0)
                    self.memory_usage_bytes = config.get("memory_usage_bytes", 0)

            mock_operators.append(MockOperator(op_config))

        # Perform validation
        validation_result = hardware_service.validate_hardware_compatibility(
            request.hardware_id, mock_operators
        )

        # Check if hardware was found
        hardware_spec = hardware_service.get_hardware_specs(request.hardware_id)
        hardware_found = hardware_spec is not None

        # Convert validation result to Pydantic model
        validation_model = ValidationResultModel(
            is_valid=validation_result.is_valid,
            errors=validation_result.errors,
            warnings=validation_result.warnings,
            recommendations=validation_result.recommendations,
        )

        return HardwareValidationResponse(
            hardware_id=request.hardware_id,
            validation_result=validation_model,
            hardware_found=hardware_found,
            timestamp=datetime.now(),
        )

    except Exception as e:
        logger.error(f"Error validating hardware compatibility: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error validating hardware compatibility: {str(e)}"
        )


@app.post(
    "/api/hardware/recommendations", response_model=List[HardwareRecommendationModel]
)
async def get_hardware_recommendations(
    workload_profile: WorkloadProfileModel,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> List[HardwareRecommendationModel]:
    """
    Get hardware recommendations based on workload profile.

    Analyzes the workload characteristics and recommends the most suitable
    hardware devices based on memory requirements, precision needs, and
    performance characteristics.

    Args:
        workload_profile: Workload characteristics for recommendation

    Returns:
        List of hardware recommendations sorted by compatibility score
    """
    try:
        # Convert Pydantic model to core WorkloadProfile
        core_workload = WorkloadProfile(
            model_type=workload_profile.model_type,
            batch_size=workload_profile.batch_size,
            sequence_length=workload_profile.sequence_length,
            precision_requirements=workload_profile.precision_requirements,
            memory_constraints=workload_profile.memory_constraints,
            latency_requirements=workload_profile.latency_requirements,
            throughput_requirements=workload_profile.throughput_requirements,
        )

        # Get recommendations
        recommendations = hardware_service.get_hardware_recommendations(core_workload)

        # Convert to Pydantic models
        recommendation_models = []
        for rec in recommendations:
            rec_model = HardwareRecommendationModel(
                hardware_id=rec.hardware_id,
                hardware_name=rec.hardware_name,
                score=rec.score,
                reasons=rec.reasons,
                estimated_performance=rec.estimated_performance,
                memory_utilization=rec.memory_utilization,
                cost_effectiveness=rec.cost_effectiveness,
            )
            recommendation_models.append(rec_model)

        return recommendation_models

    except Exception as e:
        logger.error(f"Error getting hardware recommendations: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error getting hardware recommendations: {str(e)}"
        )


@app.post("/api/hardware/reload")
async def reload_hardware_specifications(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, str]:
    """
    Reload hardware specifications from configuration files.

    This endpoint allows administrators to reload hardware specifications
    without restarting the application, useful when hardware configurations
    are updated.

    Returns:
        Status message indicating success or failure
    """
    try:
        hardware_service.reload_hardware_specifications()

        return {
            "status": "success",
            "message": "Hardware specifications reloaded successfully",
            "timestamp": datetime.now().isoformat(),
        }

    except Exception as e:
        logger.error(f"Error reloading hardware specifications: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error reloading hardware specifications: {str(e)}"
        )


# Roofline Visualization API endpoints


@app.post("/api/roofline/generate", response_model=RooflineDataModel)
async def generate_roofline_data(
    request: RooflineGenerateRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> RooflineDataModel:
    """
    Generate roofline data for multiple hardware platforms and precisions.

    This endpoint generates comprehensive roofline visualization data including:
    - Performance curves for each hardware and precision combination
    - Knee points where compute and memory limits intersect
    - Hardware specifications for reference

    Args:
        request: Roofline generation request with hardware IDs and precisions

    Returns:
        RooflineDataModel with complete roofline visualization data

    Raises:
        HTTPException: If hardware not found or roofline generation fails
    """
    try:
        # Get hardware specifications
        hardware_specs = []
        for hardware_id in request.hardware_ids:
            hardware_spec = hardware_service.get_hardware_specs(hardware_id)
            if hardware_spec is None:
                raise HTTPException(
                    status_code=404, detail=f"Hardware '{hardware_id}' not found"
                )
            hardware_specs.append(hardware_spec)

        # Generate roofline data
        roofline_data = roofline_service.generate_roofline_data(
            hardware_specs=hardware_specs, precisions=request.precisions
        )

        # Convert to Pydantic model
        return RooflineDataModel(
            operational_intensity_range=roofline_data.operational_intensity_range.tolist(),
            performance_curves={
                hw_id: {
                    precision: curve.tolist()
                    for precision, curve in precision_curves.items()
                }
                for hw_id, precision_curves in roofline_data.performance_curves.items()
            },
            knee_points={
                hw_id: {
                    precision: KneePointModel(
                        operational_intensity=knee_point.operational_intensity,
                        performance_tflops=knee_point.performance_tflops,
                        precision=knee_point.precision,
                        hardware_id=knee_point.hardware_id,
                    )
                    for precision, knee_point in precision_knees.items()
                }
                for hw_id, precision_knees in roofline_data.knee_points.items()
            },
            operator_points=roofline_data.operator_points,
            hardware_specs={
                hw_id: HardwareSpecModel(
                    id=spec.id,
                    name=spec.name,
                    type=spec.type.value,
                    architecture=spec.architecture,
                    form_factor=spec.form_factor,
                    year=spec.year,
                    memory_size_gb=spec.memory_size_gb,
                    memory_type=spec.memory_type,
                    memory_bandwidth_gbps=spec.memory_bandwidth_gbps,
                    l2_cache_mb=spec.l2_cache_mb,
                    peak_flops=spec.peak_flops,
                    tensor_performance=spec.tensor_performance,
                    vector_performance=spec.vector_performance,
                    tdp_watts=spec.tdp_watts,
                    manufacturing_process=spec.manufacturing_process,
                    interconnect=spec.interconnect,
                    supported_precisions=spec.supported_precisions,
                    tensor_cores=spec.tensor_cores,
                )
                for hw_id, spec in roofline_data.hardware_specs.items()
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error generating roofline data: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error generating roofline data: {str(e)}"
        )


@app.post("/api/roofline/plot-operators", response_model=RooflinePlotDataModel)
async def plot_operators_on_roofline(
    request: RooflinePlotOperatorsRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> RooflinePlotDataModel:
    """
    Plot operators on roofline for specific hardware.

    This endpoint positions operators on a roofline plot for a specific hardware
    platform, showing their operational intensity and achieved performance.

    Args:
        request: Request with operators and hardware ID

    Returns:
        RooflinePlotDataModel with operators positioned on roofline

    Raises:
        HTTPException: If hardware not found or operator plotting fails
    """
    try:
        # Get hardware specification
        hardware_spec = hardware_service.get_hardware_specs(request.hardware_id)
        if hardware_spec is None:
            raise HTTPException(
                status_code=404, detail=f"Hardware '{request.hardware_id}' not found"
            )

        # Convert operator configs to operator objects
        # Note: This is a simplified conversion - in practice, you'd need
        # to create actual operator instances based on the configuration
        from ..core.operators import BaseOperator

        operators = []
        for op_config in request.operators:
            # Create a mock operator for demonstration
            # In practice, you'd use a factory to create the appropriate operator type
            operator = BaseOperator(name=op_config.name, **op_config.parameters)
            if op_config.precision:
                operator.activation_precision = op_config.precision
            operators.append(operator)

        # Generate roofline plot data
        plot_data = roofline_service.plot_operators_on_roofline(
            operators=operators, hardware=hardware_spec
        )

        # Convert to Pydantic model
        return RooflinePlotDataModel(
            hardware_id=plot_data.hardware_id,
            hardware_name=plot_data.hardware_name,
            operational_intensity_range=plot_data.operational_intensity_range.tolist(),
            roofline_curves={
                precision: curve.tolist()
                for precision, curve in plot_data.roofline_curves.items()
            },
            knee_points={
                precision: KneePointModel(
                    operational_intensity=knee_point.operational_intensity,
                    performance_tflops=knee_point.performance_tflops,
                    precision=knee_point.precision,
                    hardware_id=knee_point.hardware_id,
                )
                for precision, knee_point in plot_data.knee_points.items()
            },
            operator_points=plot_data.operator_points,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error plotting operators on roofline: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error plotting operators on roofline: {str(e)}"
        )


@app.post("/api/roofline/compare", response_model=ComparisonPlotDataModel)
async def compare_hardware_rooflines(
    request: RooflineCompareRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> ComparisonPlotDataModel:
    """
    Compare rooflines across multiple hardware platforms.

    This endpoint generates comparative roofline visualizations across multiple
    hardware platforms, optionally including operator positioning.

    Args:
        request: Request with hardware IDs and optional operators

    Returns:
        ComparisonPlotDataModel with multi-hardware roofline comparison

    Raises:
        HTTPException: If hardware not found or comparison fails
    """
    try:
        # Get hardware specifications
        hardware_specs = []
        for hardware_id in request.hardware_ids:
            hardware_spec = hardware_service.get_hardware_specs(hardware_id)
            if hardware_spec is None:
                raise HTTPException(
                    status_code=404, detail=f"Hardware '{hardware_id}' not found"
                )
            hardware_specs.append(hardware_spec)

        # Convert operator configs to operator objects if provided
        operators = None
        if request.operators:
            from ..core.operators import BaseOperator

            operators = []
            for op_config in request.operators:
                operator = BaseOperator(name=op_config.name, **op_config.parameters)
                if op_config.precision:
                    operator.activation_precision = op_config.precision
                operators.append(operator)

        # Generate comparison data
        comparison_data = roofline_service.compare_hardware_rooflines(
            hardware_list=hardware_specs, operators=operators
        )

        # Convert to Pydantic model
        return ComparisonPlotDataModel(
            hardware_platforms=comparison_data.hardware_platforms,
            operational_intensity_range=comparison_data.operational_intensity_range.tolist(),
            roofline_data={
                hw_id: RooflinePlotDataModel(
                    hardware_id=plot_data.hardware_id,
                    hardware_name=plot_data.hardware_name,
                    operational_intensity_range=plot_data.operational_intensity_range.tolist(),
                    roofline_curves={
                        precision: curve.tolist()
                        for precision, curve in plot_data.roofline_curves.items()
                    },
                    knee_points={
                        precision: KneePointModel(
                            operational_intensity=knee_point.operational_intensity,
                            performance_tflops=knee_point.performance_tflops,
                            precision=knee_point.precision,
                            hardware_id=knee_point.hardware_id,
                        )
                        for precision, knee_point in plot_data.knee_points.items()
                    },
                    operator_points=plot_data.operator_points,
                )
                for hw_id, plot_data in comparison_data.roofline_data.items()
            },
            performance_rankings=comparison_data.performance_rankings,
            recommendations=comparison_data.recommendations,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing hardware rooflines: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error comparing hardware rooflines: {str(e)}"
        )


# Operator Timing Analysis API endpoints


@app.post("/api/timing/analyze", response_model=List[OperatorTimingModel])
async def analyze_operator_timing(
    request: TimingAnalyzeRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> List[OperatorTimingModel]:
    """
    Analyze operator timing on specific hardware.

    This endpoint performs comprehensive timing analysis for operators on
    specific hardware, including compute vs memory bottleneck analysis.

    Args:
        request: Request with operators and hardware ID

    Returns:
        List of OperatorTimingModel with detailed timing analysis

    Raises:
        HTTPException: If hardware not found or timing analysis fails
    """
    try:
        # Get hardware specification
        hardware_spec = hardware_service.get_hardware_specs(request.hardware_id)
        if hardware_spec is None:
            raise HTTPException(
                status_code=404, detail=f"Hardware '{request.hardware_id}' not found"
            )

        # Convert operator configs to operator objects
        from ..core.operators import BaseOperator

        operators = []
        for op_config in request.operators:
            operator = BaseOperator(name=op_config.name, **op_config.parameters)
            if op_config.precision:
                operator.activation_precision = op_config.precision
            operators.append(operator)

        # Analyze timing for each operator
        timing_results = []
        for operator in operators:
            timing = timing_service.compute_operator_timing(
                operator=operator, hardware=hardware_spec
            )
            timing_results.append(timing)

        # Convert to Pydantic models
        return [
            OperatorTimingModel(
                operator_name=timing.operator_name,
                hardware_id=timing.hardware_id,
                compute_time_ms=timing.compute_time_ms,
                memory_time_ms=timing.memory_time_ms,
                execution_time_ms=timing.execution_time_ms,
                bottleneck_type=timing.bottleneck_type,
                utilization_percent=timing.utilization_percent,
                operational_intensity=timing.operational_intensity,
                flops=timing.flops,
                memory_movement_bytes=timing.memory_movement_bytes,
                precision_overhead_factor=timing.precision_overhead_factor,
                tensor_core_utilization=timing.tensor_core_utilization,
                optimization_opportunities=timing.optimization_opportunities,
            )
            for timing in timing_results
        ]

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing operator timing: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error analyzing operator timing: {str(e)}"
        )


@app.post("/api/timing/bottlenecks", response_model=BottleneckAnalysisModel)
async def analyze_bottlenecks(
    request: TimingAnalyzeRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> BottleneckAnalysisModel:
    """
    Analyze bottlenecks across multiple operators.

    This endpoint identifies compute vs memory bottlenecks across a set of
    operators and provides optimization recommendations.

    Args:
        request: Request with operators and hardware ID

    Returns:
        BottleneckAnalysisModel with comprehensive bottleneck analysis

    Raises:
        HTTPException: If hardware not found or bottleneck analysis fails
    """
    try:
        # Get hardware specification
        hardware_spec = hardware_service.get_hardware_specs(request.hardware_id)
        if hardware_spec is None:
            raise HTTPException(
                status_code=404, detail=f"Hardware '{request.hardware_id}' not found"
            )

        # Convert operator configs to operator objects
        from ..core.operators import BaseOperator

        operators = []
        for op_config in request.operators:
            operator = BaseOperator(name=op_config.name, **op_config.parameters)
            if op_config.precision:
                operator.activation_precision = op_config.precision
            operators.append(operator)

        # Analyze bottlenecks
        bottleneck_analysis = timing_service.analyze_bottlenecks(
            operators=operators, hardware=hardware_spec
        )

        # Convert to Pydantic model
        return BottleneckAnalysisModel(
            compute_bound_operators=bottleneck_analysis.compute_bound_operators,
            memory_bound_operators=bottleneck_analysis.memory_bound_operators,
            compute_utilization_avg=bottleneck_analysis.compute_utilization_avg,
            memory_utilization_avg=bottleneck_analysis.memory_utilization_avg,
            overall_bottleneck=bottleneck_analysis.overall_bottleneck,
            recommendations=bottleneck_analysis.recommendations,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error analyzing bottlenecks: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error analyzing bottlenecks: {str(e)}"
        )


@app.post("/api/timing/compare-hardware", response_model=TimingComparisonModel)
async def compare_timing_across_hardware(
    request: TimingCompareHardwareRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> TimingComparisonModel:
    """
    Compare operator timing across multiple hardware platforms.

    This endpoint performs cross-hardware timing comparison to identify
    the best hardware for specific operators and workloads.

    Args:
        request: Request with operators and hardware IDs

    Returns:
        TimingComparisonModel with cross-hardware timing analysis

    Raises:
        HTTPException: If hardware not found or timing comparison fails
    """
    try:
        # Get hardware specifications
        hardware_specs = []
        for hardware_id in request.hardware_ids:
            hardware_spec = hardware_service.get_hardware_specs(hardware_id)
            if hardware_spec is None:
                raise HTTPException(
                    status_code=404, detail=f"Hardware '{hardware_id}' not found"
                )
            hardware_specs.append(hardware_spec)

        # Convert operator configs to operator objects
        from ..core.operators import BaseOperator

        operators = []
        for op_config in request.operators:
            operator = BaseOperator(name=op_config.name, **op_config.parameters)
            if op_config.precision:
                operator.activation_precision = op_config.precision
            operators.append(operator)

        # Perform cross-hardware timing comparison
        timing_comparison = timing_service.compare_across_hardware(
            operators=operators, hardware_list=hardware_specs
        )

        # Convert to Pydantic model
        return TimingComparisonModel(
            operators=timing_comparison.operators,
            hardware_platforms=timing_comparison.hardware_platforms,
            timing_matrix={
                op_name: {
                    hw_id: OperatorTimingModel(
                        operator_name=timing.operator_name,
                        hardware_id=timing.hardware_id,
                        compute_time_ms=timing.compute_time_ms,
                        memory_time_ms=timing.memory_time_ms,
                        execution_time_ms=timing.execution_time_ms,
                        bottleneck_type=timing.bottleneck_type,
                        utilization_percent=timing.utilization_percent,
                        operational_intensity=timing.operational_intensity,
                        flops=timing.flops,
                        memory_movement_bytes=timing.memory_movement_bytes,
                        precision_overhead_factor=timing.precision_overhead_factor,
                        tensor_core_utilization=timing.tensor_core_utilization,
                        optimization_opportunities=timing.optimization_opportunities,
                    )
                    for hw_id, timing in hw_timings.items()
                }
                for op_name, hw_timings in timing_comparison.timing_matrix.items()
            },
            performance_rankings=timing_comparison.performance_rankings,
            recommendations=timing_comparison.recommendations,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error comparing timing across hardware: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error comparing timing across hardware: {str(e)}"
        )


# Add startup and shutdown events
@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info("LLM Modeling Metrics API starting up...")
    logger.info(
        f"Rate limiting: {RATE_LIMIT_REQUESTS} requests per {RATE_LIMIT_WINDOW}s"
    )
    logger.info(f"Authentication: {'Enabled' if API_TOKEN else 'Disabled'}")
    logger.info(
        f"Supported architectures: {model_factory.get_supported_architectures()}"
    )


# Multi-Hardware Comparison API endpoints
from ..hardware.comparison_service import HardwareComparisonService
from .models import (
    CrossPlatformTimingRequest,
    CrossPlatformTimingResponse,
    HardwareSelectionWizardRequest,
    HardwareSelectionWizardResponse,
    MultiHardwareComparisonRequest,
    MultiHardwareComparisonResponse,
)

# Initialize comparison service
comparison_service = HardwareComparisonService()


@app.post("/api/hardware/compare", response_model=MultiHardwareComparisonResponse)
async def compare_hardware_platforms(
    request: MultiHardwareComparisonRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> MultiHardwareComparisonResponse:
    """
    Perform comprehensive comparison across multiple hardware platforms.

    This endpoint provides side-by-side hardware comparison including:
    - Performance metrics and specifications
    - Workload-specific analysis and recommendations
    - Cost-performance analysis
    - Roofline visualization comparison
    - Hardware recommendation engine results

    Args:
        request: Multi-hardware comparison request

    Returns:
        MultiHardwareComparisonResponse with comprehensive comparison results

    Raises:
        HTTPException: If hardware not found or comparison fails
    """
    try:
        # Validate hardware IDs
        if len(request.hardware_ids) < 2:
            raise HTTPException(
                status_code=400,
                detail="At least 2 hardware platforms required for comparison",
            )

        # Convert workload profiles if provided
        workload_profiles = []
        if request.workload_profiles:
            from ..hardware.models import WorkloadProfile

            for wp_model in request.workload_profiles:
                workload_profile = WorkloadProfile(
                    model_type=wp_model.model_type,
                    batch_size=wp_model.batch_size,
                    sequence_length=wp_model.sequence_length,
                    precision_requirements=wp_model.precision_requirements,
                    memory_constraints=wp_model.memory_constraints,
                    latency_requirements=wp_model.latency_requirements,
                    throughput_requirements=wp_model.throughput_requirements,
                )
                workload_profiles.append(workload_profile)

        # Convert operators if provided
        operators = None
        if request.operators:
            from ..core.operators import BaseOperator

            operators = []
            for op_config in request.operators:
                operator = BaseOperator(name=op_config.name, **op_config.parameters)
                if op_config.precision:
                    operator.activation_precision = op_config.precision
                operators.append(operator)

        # Perform comparison
        comparison_result = comparison_service.compare_hardware_platforms(
            hardware_ids=request.hardware_ids,
            workload_profiles=workload_profiles,
            operators=operators,
            include_cost_analysis=request.include_cost_analysis,
        )

        # Convert to response model
        return MultiHardwareComparisonResponse(
            hardware_platforms=comparison_result.hardware_platforms,
            comparison_metrics=comparison_result.comparison_metrics,
            workload_analysis=comparison_result.workload_analysis,
            roofline_comparison=comparison_result.roofline_comparison,
            timing_comparison=comparison_result.timing_comparison,
            recommendation_engine=comparison_result.recommendation_engine,
            summary_insights=comparison_result.summary_insights,
            timestamp=comparison_result.timestamp,
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in hardware comparison: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error performing hardware comparison: {str(e)}"
        )


@app.post(
    "/api/hardware/selection-wizard", response_model=HardwareSelectionWizardResponse
)
async def hardware_selection_wizard(
    request: HardwareSelectionWizardRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> HardwareSelectionWizardResponse:
    """
    Hardware selection wizard based on user requirements.

    This endpoint provides guided hardware selection based on:
    - Budget constraints
    - Use case requirements (training, inference, research, production)
    - Memory and performance requirements
    - Precision support needs

    Args:
        request: Hardware selection wizard request

    Returns:
        HardwareSelectionWizardResponse with recommendations and guidance
    """
    try:
        # Convert request to requirements dictionary
        requirements = {
            "budget": request.budget,
            "use_case": request.use_case,
            "memory_gb": request.memory_requirement_gb,
            "precisions": request.precision_requirements,
            "performance_priority": request.performance_priority,
            "power_constraint": request.power_constraint_watts,
            "form_factor": request.form_factor_preference,
        }

        # Get wizard recommendations
        wizard_result = comparison_service.create_hardware_selection_wizard(
            requirements
        )

        return HardwareSelectionWizardResponse(
            recommendations=wizard_result["recommendations"],
            guidance=wizard_result["guidance"],
            total_options=wizard_result["total_options"],
            filtered_count=wizard_result["filtered_count"],
            requirements_met=wizard_result["requirements_met"],
        )

    except Exception as e:
        logger.error(f"Error in hardware selection wizard: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error in hardware selection wizard: {str(e)}"
        )


@app.post(
    "/api/hardware/cross-platform-timing", response_model=CrossPlatformTimingResponse
)
async def cross_platform_timing_analysis(
    request: CrossPlatformTimingRequest,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> CrossPlatformTimingResponse:
    """
    Cross-platform timing analysis for operators across multiple hardware.

    This endpoint provides detailed timing analysis including:
    - Performance scaling across hardware tiers
    - Bottleneck identification per platform
    - Optimization recommendations
    - Hardware migration suggestions

    Args:
        request: Cross-platform timing analysis request

    Returns:
        CrossPlatformTimingResponse with timing analysis results
    """
    try:
        # Get hardware specifications
        hardware_specs = []
        for hardware_id in request.hardware_ids:
            hardware_spec = hardware_service.get_hardware_specs(hardware_id)
            if hardware_spec is None:
                raise HTTPException(
                    status_code=404, detail=f"Hardware '{hardware_id}' not found"
                )
            hardware_specs.append(hardware_spec)

        # Convert operators
        from ..core.operators import BaseOperator

        operators = []
        for op_config in request.operators:
            operator = BaseOperator(name=op_config.name, **op_config.parameters)
            if op_config.precision:
                operator.activation_precision = op_config.precision
            operators.append(operator)

        # Perform cross-platform timing analysis
        timing_comparison = timing_service.compare_across_hardware(
            operators=operators, hardware_list=hardware_specs
        )

        # Generate performance scaling analysis
        scaling_analysis = {}
        for operator_name in timing_comparison.operators:
            operator_timings = timing_comparison.timing_matrix[operator_name]

            # Calculate scaling factors relative to fastest hardware
            fastest_time = min(
                timing.execution_time_ms for timing in operator_timings.values()
            )
            scaling_factors = {
                hw_id: timing.execution_time_ms / fastest_time
                for hw_id, timing in operator_timings.items()
            }

            scaling_analysis[operator_name] = {
                "fastest_hardware": min(
                    operator_timings.items(), key=lambda x: x[1].execution_time_ms
                )[0],
                "scaling_factors": scaling_factors,
                "performance_range": {
                    "min_time_ms": fastest_time,
                    "max_time_ms": max(
                        timing.execution_time_ms for timing in operator_timings.values()
                    ),
                },
            }

        # Generate migration recommendations
        migration_recommendations = []

        # Find consistently best performing hardware
        hardware_scores = {hw_id: 0 for hw_id in request.hardware_ids}
        for operator_name, rankings in timing_comparison.performance_rankings.items():
            for i, hw_id in enumerate(rankings):
                hardware_scores[hw_id] += len(rankings) - i

        best_overall = max(hardware_scores.items(), key=lambda x: x[1])[0]
        migration_recommendations.append(
            f"Consider migrating to {best_overall} for overall best performance"
        )

        # Identify hardware-specific strengths
        for hw_id in request.hardware_ids:
            strong_operators = []
            for (
                operator_name,
                rankings,
            ) in timing_comparison.performance_rankings.items():
                if rankings[0] == hw_id:  # This hardware is best for this operator
                    strong_operators.append(operator_name)

            if strong_operators:
                migration_recommendations.append(
                    f"{hw_id} excels at: {', '.join(strong_operators[:3])}"
                )

        return CrossPlatformTimingResponse(
            hardware_platforms=request.hardware_ids,
            timing_comparison=timing_comparison,
            scaling_analysis=scaling_analysis,
            migration_recommendations=migration_recommendations,
            summary_statistics={
                "total_operators": len(operators),
                "total_platforms": len(hardware_specs),
                "best_overall_platform": best_overall,
                "average_performance_variance": (
                    sum(
                        max(scaling_factors.values()) / min(scaling_factors.values())
                        for scaling_factors in [
                            sa["scaling_factors"] for sa in scaling_analysis.values()
                        ]
                    )
                    / len(scaling_analysis)
                    if scaling_analysis
                    else 1.0
                ),
            },
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error in cross-platform timing analysis: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error in cross-platform timing analysis: {str(e)}"
        )


# Configuration Management API endpoints
@app.get("/api/hardware/config/summary")
async def get_configuration_summary(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get hardware configuration management summary.

    Returns information about:
    - Configuration sources and file locations
    - Auto-reload status
    - Custom profiles count
    - Hardware counts by type

    Returns:
        Dictionary with configuration summary
    """
    try:
        return hardware_service.get_configuration_summary()
    except Exception as e:
        logger.error(f"Error getting configuration summary: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error getting configuration summary: {str(e)}"
        )


@app.get("/api/hardware/config/health")
async def get_system_health(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> Dict[str, Any]:
    """
    Get system health status for hardware integration.

    Returns information about:
    - Overall system health status
    - Individual health checks
    - Error summaries and counts
    - Performance metrics

    Returns:
        Dictionary with system health information
    """
    try:
        return hardware_service.get_system_health()
    except Exception as e:
        logger.error(f"Error getting system health: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error getting system health: {str(e)}"
        )


@app.post("/api/hardware/config/validate")
async def validate_all_configurations(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> ValidationResultModel:
    """
    Validate all loaded hardware configurations.

    Performs comprehensive validation of:
    - Hardware specification completeness
    - Data type and range validation
    - Performance data consistency
    - Configuration file structure

    Returns:
        ValidationResultModel with validation results
    """
    try:
        validation_result = hardware_service.validate_all_configurations()

        return ValidationResultModel(
            is_valid=validation_result.is_valid,
            errors=validation_result.errors,
            warnings=validation_result.warnings,
            recommendations=validation_result.recommendations,
        )
    except Exception as e:
        logger.error(f"Error validating configurations: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error validating configurations: {str(e)}"
        )


@app.get("/api/hardware/config/profiles")
async def list_custom_profiles(
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> List[Dict[str, Any]]:
    """
    List all custom hardware profiles.

    Returns information about each custom profile:
    - Profile name and format
    - File path and modification time
    - File size

    Returns:
        List of custom profile information dictionaries
    """
    try:
        return hardware_service.list_custom_profiles()
    except Exception as e:
        logger.error(f"Error listing custom profiles: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error listing custom profiles: {str(e)}"
        )


@app.post("/api/hardware/config/profiles")
async def save_custom_profile(
    profile_name: str,
    profile_data: Dict[str, Any],
    format: str = "yaml",
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> ValidationResultModel:
    """
    Save a custom hardware profile.

    Args:
        profile_name: Name for the profile file
        profile_data: Hardware configuration data
        format: File format ('yaml' or 'json')

    Returns:
        ValidationResultModel indicating success/failure
    """
    try:
        if format not in ["yaml", "json"]:
            raise HTTPException(
                status_code=400, detail="Format must be 'yaml' or 'json'"
            )

        result = hardware_service.save_custom_profile(
            profile_name, profile_data, format
        )

        return ValidationResultModel(
            is_valid=result.is_valid,
            errors=result.errors,
            warnings=result.warnings,
            recommendations=result.recommendations,
        )
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error saving custom profile: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error saving custom profile: {str(e)}"
        )


@app.delete("/api/hardware/config/profiles/{profile_name}")
async def delete_custom_profile(
    profile_name: str,
    client_ip: str = Depends(rate_limit_dependency),
    authenticated: bool = Depends(verify_token),
) -> ValidationResultModel:
    """
    Delete a custom hardware profile.

    Args:
        profile_name: Name of the profile to delete

    Returns:
        ValidationResultModel indicating success/failure
    """
    try:
        result = hardware_service.delete_custom_profile(profile_name)

        return ValidationResultModel(
            is_valid=result.is_valid,
            errors=result.errors,
            warnings=result.warnings,
            recommendations=result.recommendations,
        )
    except Exception as e:
        logger.error(f"Error deleting custom profile: {e}")
        raise HTTPException(
            status_code=500, detail=f"Error deleting custom profile: {str(e)}"
        )


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("LLM Modeling Metrics API shutting down...")

    # Close all WebSocket connections
    for client_id in list(manager.active_connections.keys()):
        try:
            await manager.active_connections[client_id].close()
        except Exception as e:
            logger.error(f"Error closing WebSocket for {client_id}: {e}")
        finally:
            manager.disconnect(client_id)

    logger.info("Shutdown complete")


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000, reload=True)
