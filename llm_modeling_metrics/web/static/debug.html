<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug - LLM Modeling Metrics</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css"
        rel="stylesheet">
</head>

<body>
    <div class="container mt-4">
        <h1>Debug - LLM Modeling Metrics</h1>

        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Debug Information</h5>
                    </div>
                    <div class="card-body">
                        <div id="debug-info">
                            <p>Loading...</p>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>Model Selection Test</h5>
                    </div>
                    <div class="card-body">
                        <label for="modelSelect" class="form-label">Select Models</label>
                        <select id="modelSelect" class="form-select" multiple="multiple" style="width: 100%">
                            <!-- Options will be populated dynamically -->
                        </select>
                        <div class="mt-2">
                            <button id="testBtn" class="btn btn-primary">Test Selection</button>
                        </div>
                        <div id="selection-result" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5>Quick Presets Test</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-outline-primary me-2" data-preset="dense-comparison">
                            Llama Models
                        </button>
                        <button class="btn btn-outline-primary me-2" data-preset="moe-comparison">
                            MoE Models
                        </button>
                        <button class="btn btn-outline-primary" data-preset="size-comparison">
                            Size Comparison
                        </button>
                        <div id="preset-result" class="mt-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>

    <script>
        $(document).ready(function () {
            const debugInfo = $('#debug-info');
            const modelSelect = $('#modelSelect');
            const selectionResult = $('#selection-result');
            const presetResult = $('#preset-result');

            // Debug information
            debugInfo.html(`
                <strong>Library Status:</strong><br>
                jQuery: ${typeof $ !== 'undefined' ? '✓' : '✗'}<br>
                Select2: ${typeof $.fn.select2 !== 'undefined' ? '✓' : '✗'}<br>
                Bootstrap: ${typeof bootstrap !== 'undefined' ? '✓' : '✗'}<br>
                <br>
                <strong>API Test:</strong><br>
                <span id="api-status">Testing...</span>
            `);

            // Test API connection
            fetch('/api/models/supported')
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API Response:', data);
                    $('#api-status').html('✓ Connected<br>Architectures: ' + data.architectures.length);

                    // Populate model select
                    modelSelect.empty();
                    Object.entries(data.architecture_info).forEach(([arch, info]) => {
                        if (info.examples && info.examples.length > 0) {
                            const optgroup = $(`<optgroup label="${arch.toUpperCase()}">`);
                            info.examples.forEach(model => {
                                optgroup.append(`<option value="${model}">${model}</option>`);
                            });
                            modelSelect.append(optgroup);
                        }
                    });

                    // Initialize Select2
                    try {
                        modelSelect.select2({
                            theme: 'bootstrap-5',
                            placeholder: 'Search and select models...',
                            allowClear: true,
                            closeOnSelect: false
                        });

                        debugInfo.append('<br><strong>Select2:</strong> ✓ Initialized');

                        // Test if Select2 is clickable
                        setTimeout(() => {
                            const select2Container = $('.select2-container');
                            if (select2Container.length > 0) {
                                debugInfo.append('<br><strong>Select2 Container:</strong> ✓ Found');

                                // Check if it's clickable
                                select2Container.on('click', function () {
                                    debugInfo.append('<br><strong>Click Event:</strong> ✓ Triggered');
                                });
                            } else {
                                debugInfo.append('<br><strong>Select2 Container:</strong> ✗ Not found');
                            }
                        }, 500);

                    } catch (error) {
                        console.error('Select2 initialization failed:', error);
                        debugInfo.append('<br><strong>Select2:</strong> ✗ Failed - ' + error.message);
                    }
                })
                .catch(error => {
                    console.error('API test failed:', error);
                    $('#api-status').html('✗ Failed: ' + error.message);
                });

            // Model selection change handler
            modelSelect.on('change', function () {
                const selected = $(this).val() || [];
                selectionResult.html(`<div class="alert alert-info">Selected ${selected.length} models: ${selected.join(', ')}</div>`);
            });

            // Test button
            $('#testBtn').click(function () {
                const selected = modelSelect.val() || [];
                selectionResult.html(`<div class="alert alert-success">Test clicked! Selected: ${selected.length} models</div>`);
            });

            // Preset buttons
            const modelPresets = {
                'dense-comparison': [
                    'meta-llama/Llama-2-7b-hf',
                    'meta-llama/Llama-2-13b-hf'
                ],
                'moe-comparison': [
                    'mistralai/Mixtral-8x7B-v0.1'
                ],
                'size-comparison': [
                    'meta-llama/Llama-2-7b-hf',
                    'meta-llama/Llama-2-13b-hf'
                ]
            };

            $('[data-preset]').click(function () {
                const preset = $(this).data('preset');
                const models = modelPresets[preset];

                if (models) {
                    modelSelect.val(models).trigger('change');
                    presetResult.html(`<div class="alert alert-success">Applied ${preset}: ${models.join(', ')}</div>`);
                } else {
                    presetResult.html(`<div class="alert alert-danger">Preset ${preset} not found</div>`);
                }
            });
        });
    </script>
</body>

</html>
