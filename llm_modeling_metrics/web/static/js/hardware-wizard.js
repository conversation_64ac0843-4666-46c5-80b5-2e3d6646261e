/**
 * Hardware Selection Wizard
 * Provides guided hardware selection based on user requirements
 */

class HardwareSelectionWizard {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.requirements = {};
        this.recommendations = null;

        this.initializeEventListeners();
        this.showStep(1);
    }

    initializeEventListeners() {
        // Step navigation
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('next-step-btn')) {
                this.nextStep();
            } else if (e.target.classList.contains('prev-step-btn')) {
                this.prevStep();
            } else if (e.target.classList.contains('restart-wizard-btn')) {
                this.restartWizard();
            }
        });

        // Form input changes
        document.addEventListener('change', (e) => {
            if (e.target.closest('.wizard-step')) {
                this.updateRequirements();
            }
        });

        // Get recommendations button
        const getRecsBtn = document.getElementById('get-recommendations-btn');
        if (getRecsBtn) {
            getRecsBtn.addEventListener('click', () => this.getRecommendations());
        }
    }

    showStep(stepNumber) {
        // Hide all steps
        document.querySelectorAll('.wizard-step').forEach(step => {
            step.style.display = 'none';
        });

        // Show current step
        const currentStepEl = document.getElementById(`wizard-step-${stepNumber}`);
        if (currentStepEl) {
            currentStepEl.style.display = 'block';
        }

        // Update progress indicator
        this.updateProgressIndicator(stepNumber);

        // Update navigation buttons
        this.updateNavigationButtons(stepNumber);

        this.currentStep = stepNumber;
    }

    updateProgressIndicator(stepNumber) {
        const progressBar = document.querySelector('.wizard-progress-bar');
        if (progressBar) {
            const progress = (stepNumber / this.totalSteps) * 100;
            progressBar.style.width = `${progress}%`;
        }

        // Update step indicators
        document.querySelectorAll('.step-indicator').forEach((indicator, index) => {
            const step = index + 1;
            indicator.classList.remove('active', 'completed');

            if (step === stepNumber) {
                indicator.classList.add('active');
            } else if (step < stepNumber) {
                indicator.classList.add('completed');
            }
        });

        // Update step title
        const stepTitle = document.getElementById('wizard-step-title');
        if (stepTitle) {
            const titles = [
                'Use Case & Budget',
                'Performance Requirements',
                'Technical Specifications',
                'Recommendations'
            ];
            stepTitle.textContent = `Step ${stepNumber}: ${titles[stepNumber - 1]}`;
        }
    }

    updateNavigationButtons(stepNumber) {
        const prevBtn = document.querySelector('.prev-step-btn');
        const nextBtn = document.querySelector('.next-step-btn');

        if (prevBtn) {
            prevBtn.style.display = stepNumber > 1 ? 'inline-block' : 'none';
        }

        if (nextBtn) {
            if (stepNumber < this.totalSteps) {
                nextBtn.style.display = 'inline-block';
                nextBtn.textContent = 'Next Step';
            } else {
                nextBtn.style.display = 'none';
            }
        }
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            if (this.currentStep < this.totalSteps) {
                this.showStep(this.currentStep + 1);
            }
        }
    }

    prevStep() {
        if (this.currentStep > 1) {
            this.showStep(this.currentStep - 1);
        }
    }

    validateCurrentStep() {
        const currentStepEl = document.getElementById(`wizard-step-${this.currentStep}`);
        if (!currentStepEl) return true;

        // Validate required fields in current step
        const requiredFields = currentStepEl.querySelectorAll('[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('error');
                isValid = false;
            } else {
                field.classList.remove('error');
            }
        });

        if (!isValid) {
            this.showError('Please fill in all required fields');
        }

        return isValid;
    }

    updateRequirements() {
        // Collect requirements from all form fields
        const formData = new FormData(document.getElementById('wizard-form'));

        this.requirements = {
            budget: formData.get('budget') ? parseFloat(formData.get('budget')) : null,
            use_case: formData.get('use_case') || 'general',
            memory_requirement_gb: formData.get('memory_requirement') ?
                parseInt(formData.get('memory_requirement')) : null,
            precision_requirements: formData.getAll('precision_requirements'),
            performance_priority: formData.get('performance_priority') || 'balanced',
            power_constraint_watts: formData.get('power_constraint') ?
                parseInt(formData.get('power_constraint')) : null,
            form_factor_preference: formData.get('form_factor') || null
        };

        // Update requirement summary
        this.updateRequirementSummary();
    }

    updateRequirementSummary() {
        const summaryEl = document.getElementById('requirements-summary');
        if (!summaryEl) return;

        const summary = [];

        if (this.requirements.budget) {
            summary.push(`Budget: $${this.requirements.budget.toLocaleString()}`);
        }

        if (this.requirements.use_case) {
            summary.push(`Use Case: ${this.requirements.use_case}`);
        }

        if (this.requirements.memory_requirement_gb) {
            summary.push(`Memory: ${this.requirements.memory_requirement_gb} GB`);
        }

        if (this.requirements.precision_requirements.length > 0) {
            summary.push(`Precisions: ${this.requirements.precision_requirements.join(', ')}`);
        }

        if (this.requirements.performance_priority) {
            summary.push(`Priority: ${this.requirements.performance_priority}`);
        }

        summaryEl.innerHTML = summary.length > 0 ?
            `<ul>${summary.map(item => `<li>${item}</li>`).join('')}</ul>` :
            '<p>No requirements specified yet</p>';
    }

    async getRecommendations() {
        this.showLoading(true);

        try {
            const response = await fetch('/api/hardware/selection-wizard', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(this.requirements)
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            this.recommendations = await response.json();
            this.renderRecommendations();

        } catch (error) {
            console.error('Error getting recommendations:', error);
            this.showError('Failed to get hardware recommendations: ' + error.message);
        } finally {
            this.showLoading(false);
        }
    }

    renderRecommendations() {
        const container = document.getElementById('recommendations-container');
        if (!container) return;

        if (!this.recommendations.requirements_met) {
            container.innerHTML = `
                <div class="no-recommendations">
                    <h3>No Hardware Meets Requirements</h3>
                    <p>Unfortunately, no hardware in our database meets all your specified requirements.</p>
                    <div class="alternatives">
                        <h4>Suggestions:</h4>
                        <ul>
                            ${this.recommendations.guidance.alternatives.map(alt =>
                                `<li>${alt}</li>`
                            ).join('')}
                        </ul>
                    </div>
                    <button class="restart-wizard-btn btn btn-primary">
                        Adjust Requirements
                    </button>
                </div>
            `;
            return;
        }

        const html = `
            <div class="recommendations-header">
                <h3>Hardware Recommendations</h3>
                <p class="recommendations-summary">${this.recommendations.guidance.summary}</p>
            </div>

            <div class="recommendations-list">
                ${this.recommendations.recommendations.map(rec => this.renderRecommendationCard(rec)).join('')}
            </div>

            <div class="selection-guidance">
                <h4>Selection Guidance</h4>
                <div class="guidance-content">
                    <div class="key-considerations">
                        <h5>Key Considerations</h5>
                        <ul>
                            ${this.recommendations.guidance.key_considerations.map(consideration =>
                                `<li>${consideration}</li>`
                            ).join('')}
                        </ul>
                    </div>

                    <div class="next-steps">
                        <h5>Next Steps</h5>
                        <ul>
                            ${this.recommendations.guidance.next_steps.map(step =>
                                `<li>${step}</li>`
                            ).join('')}
                        </ul>
                    </div>
                </div>
            </div>

            <div class="wizard-actions">
                <button class="restart-wizard-btn btn btn-secondary">
                    Start Over
                </button>
                <button class="compare-recommended-btn btn btn-primary"
                        onclick="this.compareRecommended()">
                    Compare Top 3
                </button>
            </div>
        `;

        container.innerHTML = html;
    }

    renderRecommendationCard(recommendation) {
        const rankClass = recommendation.rank <= 3 ? 'top-recommendation' : '';
        const costDisplay = recommendation.cost ?
            `$${recommendation.cost.toLocaleString()}` : 'Price on request';

        return `
            <div class="recommendation-card ${rankClass}">
                <div class="recommendation-header">
                    <div class="rank-badge">#${recommendation.rank}</div>
                    <div class="hardware-info">
                        <h4 class="hardware-name">${recommendation.hardware_name}</h4>
                        <div class="hardware-score">
                            Score: ${recommendation.score.toFixed(0)}/100
                        </div>
                    </div>
                    <div class="hardware-cost">${costDisplay}</div>
                </div>

                <div class="recommendation-content">
                    <div class="reasons">
                        <h5>Why This Hardware?</h5>
                        <ul class="reasons-list">
                            ${recommendation.reasons.map(reason =>
                                `<li>${reason}</li>`
                            ).join('')}
                        </ul>
                    </div>

                    <div class="pros-cons">
                        <div class="pros">
                            <h6>Advantages</h6>
                            <ul>
                                ${recommendation.pros.map(pro =>
                                    `<li class="pro-item">${pro}</li>`
                                ).join('')}
                            </ul>
                        </div>

                        <div class="cons">
                            <h6>Considerations</h6>
                            <ul>
                                ${recommendation.cons.map(con =>
                                    `<li class="con-item">${con}</li>`
                                ).join('')}
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="recommendation-actions">
                    <button class="btn btn-outline"
                            onclick="this.viewHardwareDetails('${recommendation.hardware_id}')">
                        View Details
                    </button>
                    <button class="btn btn-primary"
                            onclick="this.selectHardware('${recommendation.hardware_id}')">
                        Select This Hardware
                    </button>
                </div>
            </div>
        `;
    }

    compareRecommended() {
        if (!this.recommendations || this.recommendations.recommendations.length === 0) {
            this.showError('No recommendations available for comparison');
            return;
        }

        // Get top 3 recommendations
        const topRecommendations = this.recommendations.recommendations.slice(0, 3);
        const hardwareIds = topRecommendations.map(rec => rec.hardware_id);

        // Switch to comparison interface
        if (window.hardwareComparison) {
            // Clear current selection
            window.hardwareComparison.clearSelection();

            // Select recommended hardware
            hardwareIds.forEach(hwId => {
                const checkbox = document.getElementById(`hw-${hwId}`);
                if (checkbox) {
                    checkbox.checked = true;
                    window.hardwareComparison.selectedHardware.add(hwId);
                }
            });

            // Update UI and perform comparison
            window.hardwareComparison.updateSelectionUI();
            window.hardwareComparison.performComparison();

            // Switch to comparison tab
            this.switchToComparisonTab();
        }
    }

    switchToComparisonTab() {
        // Switch to hardware comparison tab if using tabs
        const comparisonTab = document.querySelector('[data-tab="hardware-comparison"]');
        if (comparisonTab) {
            comparisonTab.click();
        }

        // Or scroll to comparison section
        const comparisonSection = document.getElementById('hardware-comparison-container');
        if (comparisonSection) {
            comparisonSection.scrollIntoView({ behavior: 'smooth' });
        }
    }

    viewHardwareDetails(hardwareId) {
        // Open hardware details modal or navigate to details page
        if (window.hardwareDetails) {
            window.hardwareDetails.showDetails(hardwareId);
        } else {
            // Fallback: open in new tab
            window.open(`/hardware/${hardwareId}`, '_blank');
        }
    }

    selectHardware(hardwareId) {
        // Handle hardware selection
        if (window.hardwareComparison) {
            window.hardwareComparison.clearSelection();

            const checkbox = document.getElementById(`hw-${hardwareId}`);
            if (checkbox) {
                checkbox.checked = true;
                window.hardwareComparison.selectedHardware.add(hardwareId);
                window.hardwareComparison.updateSelectionUI();
            }

            this.switchToComparisonTab();
        }

        // Close wizard
        this.closeWizard();
    }

    restartWizard() {
        this.currentStep = 1;
        this.requirements = {};
        this.recommendations = null;

        // Reset form
        const form = document.getElementById('wizard-form');
        if (form) {
            form.reset();
        }

        // Clear recommendations
        const container = document.getElementById('recommendations-container');
        if (container) {
            container.innerHTML = '';
        }

        // Show first step
        this.showStep(1);
    }

    closeWizard() {
        const wizardModal = document.getElementById('hardware-wizard-modal');
        if (wizardModal) {
            wizardModal.style.display = 'none';
        }
    }

    openWizard() {
        const wizardModal = document.getElementById('hardware-wizard-modal');
        if (wizardModal) {
            wizardModal.style.display = 'block';
            this.restartWizard();
        }
    }

    showLoading(show) {
        const loader = document.getElementById('wizard-loader');
        if (loader) {
            loader.style.display = show ? 'block' : 'none';
        }

        const getRecsBtn = document.getElementById('get-recommendations-btn');
        if (getRecsBtn) {
            getRecsBtn.disabled = show;
            getRecsBtn.textContent = show ? 'Getting Recommendations...' : 'Get Recommendations';
        }
    }

    showError(message) {
        const errorContainer = document.getElementById('wizard-error-container');
        if (errorContainer) {
            errorContainer.innerHTML = `
                <div class="alert alert-error">
                    <span class="error-message">${message}</span>
                    <button class="close-btn" onclick="this.parentElement.style.display='none'">&times;</button>
                </div>
            `;
            errorContainer.style.display = 'block';
        } else {
            alert(message);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (document.getElementById('hardware-wizard-container')) {
        window.hardwareWizard = new HardwareSelectionWizard();

        // Add wizard trigger button event
        const wizardTrigger = document.getElementById('open-hardware-wizard');
        if (wizardTrigger) {
            wizardTrigger.addEventListener('click', () => {
                window.hardwareWizard.openWizard();
            });
        }
    }
});
