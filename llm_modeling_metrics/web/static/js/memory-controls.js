/**
 * Memory Controls JavaScript Module
 * Provides advanced memory visualization and control functionality for LLM analysis
 */

class MemoryControls {
    constructor(dashboard) {
        this.dashboard = dashboard;
        this.state = {
            showTotalMemory: false,
            showKvGrowthChart: false,
            kvCacheDtype: 'fp16',
            minSequenceLength: 512,
            maxSequenceLength: 32768,
            supportedDtypes: ['fp16', 'bf16', 'fp32', 'int8'],
            validationErrors: [],
            isInitialized: false
        };

        // Chart instance for KV growth visualization
        this.kvGrowthChart = null;

        // Event listeners storage for cleanup
        this.eventListeners = [];

        // Initialize the controls
        this.init();
    }

    /**
     * Initialize memory controls
     */
    async init() {
        try {
            console.log('Initializing MemoryControls...');

            // Load supported data types from API
            await this.loadSupportedDtypes();

            // Initialize UI components
            this.initializeComponents();

            // Bind event handlers
            this.bindEvents();

            // Set initial state
            this.updateUI();

            // Validate initial state
            this.validateSequenceLengthRange();

            this.state.isInitialized = true;
            console.log('MemoryControls initialized successfully');

            // Emit initialization event
            this.emitStateChange('initialized', true, this.state);

        } catch (error) {
            console.error('Failed to initialize MemoryControls:', error);
            throw error;
        }
    }

    /**
     * Initialize UI components
     */
    initializeComponents() {
        // Initialize memory toggle switch
        this.initializeMemoryToggle();

        // Initialize KV cache dtype selector
        this.initializeKvDtypeSelector();

        // Initialize sequence length range sliders
        this.initializeSequenceLengthControls();

        // Initialize KV growth chart toggle
        this.initializeKvGrowthToggle();

        // Initialize validation display
        this.initializeValidationDisplay();
    }

    /**
     * Initialize memory toggle switch component
     */
    initializeMemoryToggle() {
        const toggle = document.getElementById('showTotalMemoryToggle');
        if (!toggle) {
            console.warn('Memory toggle switch not found');
            return;
        }

        // Set initial state
        toggle.checked = this.state.showTotalMemory;

        // Update visibility based on initial state
        this.updateMemoryControlsVisibility();

        console.log('Memory toggle switch initialized');
    }

    /**
     * Initialize KV cache dtype selector dropdown component
     */
    initializeKvDtypeSelector() {
        const selector = document.getElementById('kvCacheDtype');
        if (!selector) {
            console.warn('KV cache dtype selector not found');
            return;
        }

        // Populate options
        this.updateKvDtypeOptions();

        // Set initial value
        selector.value = this.state.kvCacheDtype;

        console.log('KV cache dtype selector initialized');
    }

    /**
     * Initialize sequence length range sliders with validation
     */
    initializeSequenceLengthControls() {
        const minInput = document.getElementById('minSequenceLength');
        const maxInput = document.getElementById('maxSequenceLength');

        if (!minInput || !maxInput) {
            console.warn('Sequence length controls not found');
            return;
        }

        // Set initial values (convert from tokens to thousands)
        minInput.value = this.state.minSequenceLength / 1000;
        maxInput.value = this.state.maxSequenceLength / 1000;

        // Set input constraints (in thousands)
        minInput.min = 0.5;
        minInput.max = 32;
        minInput.step = 0.5;

        maxInput.min = 1;
        maxInput.max = 32;
        maxInput.step = 1;

        console.log('Sequence length controls initialized');
    }

    /**
     * Initialize KV growth chart toggle
     */
    initializeKvGrowthToggle() {
        const toggle = document.getElementById('showKvGrowthToggle');
        if (!toggle) {
            console.warn('KV growth chart toggle not found');
            return;
        }

        // Set initial state
        toggle.checked = this.state.showKvGrowthChart;

        console.log('KV growth chart toggle initialized');
    }

    /**
     * Initialize validation display area
     */
    initializeValidationDisplay() {
        let validationDiv = document.getElementById('sequenceLengthValidation');
        if (!validationDiv) {
            // Create validation div if it doesn't exist
            validationDiv = document.createElement('div');
            validationDiv.id = 'sequenceLengthValidation';
            validationDiv.className = 'mt-2';

            // Insert after sequence length controls
            const kvGrowthSection = document.querySelector('#kvCacheConfig .mb-3:nth-child(2)');
            if (kvGrowthSection) {
                kvGrowthSection.appendChild(validationDiv);
            }
        }

        console.log('Validation display initialized');
    }

    /**
     * Bind event handlers for control interactions and real-time updates
     */
    bindEvents() {
        // Memory toggle switch event
        this.addEventHandler('showTotalMemoryToggle', 'change', (e) => {
            this.handleMemoryToggleChange(e.target.checked);
        });

        // KV cache dtype selector event
        this.addEventHandler('kvCacheDtype', 'change', (e) => {
            this.handleKvDtypeChange(e.target.value);
        });

        // Sequence length range events with real-time validation (convert from thousands to tokens)
        this.addEventHandler('minSequenceLength', 'input', (e) => {
            this.handleSequenceLengthChange('min', Math.round(parseFloat(e.target.value) * 1000));
        });

        this.addEventHandler('maxSequenceLength', 'input', (e) => {
            this.handleSequenceLengthChange('max', Math.round(parseFloat(e.target.value) * 1000));
        });

        // KV growth chart toggle event
        this.addEventHandler('showKvGrowthToggle', 'change', (e) => {
            this.handleKvGrowthToggleChange(e.target.checked);
        });

        // Listen for external state changes
        document.addEventListener('memoryControlsExternalChange', (e) => {
            this.handleExternalStateChange(e.detail);
        });

        console.log('Event handlers bound successfully');
    }

    /**
     * Helper method to add event listeners with cleanup tracking
     */
    addEventHandler(elementId, event, handler) {
        const element = document.getElementById(elementId);
        if (element) {
            element.addEventListener(event, handler);
            this.eventListeners.push({ element, event, handler });
        } else {
            console.warn(`Element ${elementId} not found for event binding`);
        }
    }

    /**
     * Load supported data types from API
     */
    async loadSupportedDtypes() {
        try {
            const response = await fetch('/api/memory/dtypes');
            if (response.ok) {
                const data = await response.json();
                this.state.supportedDtypes = data.dtypes || this.state.supportedDtypes;
                console.log('Supported dtypes loaded:', this.state.supportedDtypes);
            } else {
                console.warn('Could not load supported dtypes, using defaults');
            }
        } catch (error) {
            console.warn('Failed to load supported dtypes:', error);
            // Continue with default dtypes
        }
    }

    /**
     * Update KV dtype selector options
     */
    updateKvDtypeOptions() {
        const selector = document.getElementById('kvCacheDtype');
        if (!selector) return;

        const currentValue = selector.value;
        selector.innerHTML = '';

        const dtypeNames = {
            'fp16': 'FP16 (16-bit float)',
            'bf16': 'BF16 (bfloat16)',
            'fp32': 'FP32 (32-bit float)',
            'int8': 'INT8 (8-bit integer)',
            'int4': 'INT4 (4-bit integer)'
        };

        this.state.supportedDtypes.forEach(dtype => {
            const option = document.createElement('option');
            option.value = dtype;
            option.textContent = dtypeNames[dtype] || dtype.toUpperCase();
            if (dtype === currentValue || dtype === this.state.kvCacheDtype) {
                option.selected = true;
            }
            selector.appendChild(option);
        });
    }

    /**
     * Handle memory toggle switch changes
     */
    handleMemoryToggleChange(showMemory) {
        console.log('Memory toggle changed:', showMemory);

        // Update state
        this.state.showTotalMemory = showMemory;

        // Update UI visibility
        this.updateMemoryControlsVisibility();

        // Emit state change event
        this.emitStateChange('showTotalMemory', showMemory, this.state);

        // Show user feedback
        const message = showMemory ? 'Memory analysis enabled' : 'Memory analysis disabled';
        this.showToast(message, 'info');
    }

    /**
     * Handle KV cache dtype changes
     */
    handleKvDtypeChange(dtype) {
        console.log('KV cache dtype changed:', dtype);

        // Validate dtype
        if (!this.state.supportedDtypes.includes(dtype)) {
            console.warn('Unsupported dtype selected:', dtype);
            this.showToast('Unsupported data type selected', 'warning');
            return;
        }

        // Update state
        this.state.kvCacheDtype = dtype;

        // Re-validate sequence length range (memory usage may change)
        this.validateSequenceLengthRange();

        // Emit state change event
        this.emitStateChange('kvCacheDtype', dtype, this.state);

        // Show user feedback
        this.showToast(`KV cache dtype changed to ${dtype.toUpperCase()}`, 'info');
    }

    /**
     * Handle sequence length range changes
     */
    handleSequenceLengthChange(type, value) {
        console.log(`Sequence length ${type} changed:`, value);

        // Update state
        if (type === 'min') {
            this.state.minSequenceLength = value;
        } else if (type === 'max') {
            this.state.maxSequenceLength = value;
        }

        // Validate range in real-time
        this.validateSequenceLengthRange();

        // Emit state change event
        this.emitStateChange('sequenceLength', { min: this.state.minSequenceLength, max: this.state.maxSequenceLength }, this.state);
    }

    /**
     * Enhanced sequence length range validation with user feedback
     */
    validateSequenceLengthRange() {
        const validationDiv = document.getElementById('sequenceLengthValidation');
        if (!validationDiv) return true;

        const min = this.state.minSequenceLength;
        const max = this.state.maxSequenceLength;

        // Clear previous validation
        validationDiv.innerHTML = '';
        this.state.validationErrors = [];

        const errors = [];
        const warnings = [];
        const info = [];

        // Core validation rules (512-32768)
        if (isNaN(min) || isNaN(max)) {
            errors.push('Sequence lengths must be valid numbers');
        } else {
            if (min >= max) {
                errors.push('Minimum sequence length must be less than maximum');
            }

            if (min < 512) {
                errors.push('Minimum sequence length must be at least 512 tokens');
            }

            if (max > 32768) {
                errors.push('Maximum sequence length cannot exceed 32768 tokens');
            }

            if (min > 32768) {
                errors.push('Minimum sequence length cannot exceed 32768 tokens');
            }

            if (max < 512) {
                errors.push('Maximum sequence length must be at least 512 tokens');
            }

            // Performance and usability warnings
            if (errors.length === 0) {
                const dataPoints = Math.ceil((max - min) / 1024);

                if (dataPoints > 50) {
                    warnings.push(`Large range will generate ${dataPoints} data points - may impact performance`);
                }

                if (dataPoints < 3) {
                    warnings.push('Small range may not provide meaningful visualization');
                }

                if (max - min < 1024) {
                    warnings.push('Range is smaller than default step size (1024) - consider adjusting');
                }

                // Helpful information
                info.push(`Will generate approximately ${dataPoints} data points`);

                if (min >= 8192 || max >= 16384) {
                    info.push('Long sequences may require significant memory');
                }

                // Memory estimation
                const estimatedMemoryGB = this.estimateSequenceLengthMemoryUsage(min, max, dataPoints);
                if (estimatedMemoryGB > 1) {
                    info.push(`Estimated memory usage: ~${estimatedMemoryGB.toFixed(1)}GB`);
                }
            }
        }

        // Store validation errors in state
        this.state.validationErrors = errors;

        // Display validation results with enhanced styling
        if (errors.length > 0) {
            validationDiv.innerHTML = `
                <div class="alert alert-danger py-2 mb-0">
                    <i class="fas fa-exclamation-circle me-1"></i>
                    <strong>Validation Error:</strong>
                    <ul class="mb-0 mt-1">
                        ${errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                    <div class="mt-2">
                        <small class="text-muted">
                            <i class="fas fa-lightbulb me-1"></i>
                            Tip: Valid range is 512-32768 tokens with minimum less than maximum
                        </small>
                    </div>
                </div>
            `;
            this.setSequenceLengthInputValidationState('invalid');
            return false;
        } else if (warnings.length > 0) {
            validationDiv.innerHTML = `
                <div class="alert alert-warning py-2 mb-0">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    <strong>Warning:</strong>
                    <ul class="mb-0 mt-1">
                        ${warnings.map(warning => `<li>${warning}</li>`).join('')}
                    </ul>
                    ${info.length > 0 ? `<div class="mt-1 text-muted small">${info.join(', ')}</div>` : ''}
                </div>
            `;
            this.setSequenceLengthInputValidationState('warning');
        } else {
            validationDiv.innerHTML = `
                <div class="alert alert-success py-2 mb-0">
                    <i class="fas fa-check-circle me-1"></i>
                    <strong>Valid range:</strong> ${min} - ${max} tokens
                    <div class="text-muted small mt-1">${info.join(', ')}</div>
                </div>
            `;
            this.setSequenceLengthInputValidationState('valid');
        }

        return errors.length === 0;
    }

    /**
     * Estimate memory usage for sequence length analysis
     */
    estimateSequenceLengthMemoryUsage(min, max, dataPoints) {
        const avgSequenceLength = (min + max) / 2;
        const bytesPerToken = this.getDtypeBytes(this.state.kvCacheDtype);
        const estimatedBytesPerPoint = avgSequenceLength * bytesPerToken * 1024; // Rough multiplier
        return (estimatedBytesPerPoint * dataPoints) / (1024 * 1024 * 1024); // Convert to GB
    }

    /**
     * Get bytes per element for dtype
     */
    getDtypeBytes(dtype) {
        const dtypeBytes = {
            'fp32': 4,
            'fp16': 2,
            'bf16': 2,
            'int8': 1,
            'int4': 0.5
        };
        return dtypeBytes[dtype] || 2; // Default to fp16
    }

    /**
     * Set validation state for sequence length inputs
     */
    setSequenceLengthInputValidationState(state) {
        const inputs = [
            document.getElementById('minSequenceLength'),
            document.getElementById('maxSequenceLength')
        ];

        inputs.forEach(input => {
            if (!input) return;

            // Remove existing validation classes
            input.classList.remove('is-valid', 'is-invalid');

            // Add appropriate class based on state
            if (state === 'valid') {
                input.classList.add('is-valid');
            } else if (state === 'invalid') {
                input.classList.add('is-invalid');
            }
            // 'warning' state doesn't add Bootstrap validation classes
        });
    }

    /**
     * Handle KV growth chart toggle change
     */
    handleKvGrowthToggleChange(showChart) {
        console.log('KV growth chart toggle changed:', showChart);

        // Update state
        this.state.showKvGrowthChart = showChart;

        // Emit state change event
        this.emitStateChange('showKvGrowthChart', showChart, this.state);

        if (showChart) {
            // Validate current state before showing chart
            if (!this.validateSequenceLengthRange()) {
                this.showToast('Please fix validation errors before enabling chart', 'error');
                // Reset toggle if validation fails
                const toggle = document.getElementById('showKvGrowthToggle');
                if (toggle) toggle.checked = false;
                this.state.showKvGrowthChart = false;
                return;
            }

            // Request chart generation from dashboard
            this.handleKvGrowthRequest();
            this.showToast('KV memory growth chart enabled', 'info');
        } else {
            // Hide chart if dashboard supports it
            if (this.dashboard && typeof this.dashboard.hideKvGrowthChart === 'function') {
                this.dashboard.hideKvGrowthChart();
            }
            this.showToast('KV memory growth chart disabled', 'info');
        }
    }

    /**
     * Handle KV growth chart request
     */
    async handleKvGrowthRequest() {
        console.log('KV growth chart requested');

        // Validate current state
        if (!this.validateSequenceLengthRange()) {
            this.showToast('Please fix validation errors before generating chart', 'error');
            return;
        }

        // Check if models are selected (delegate to dashboard)
        if (this.dashboard && typeof this.dashboard.showKvGrowthChart === 'function') {
            await this.dashboard.showKvGrowthChart();
        } else {
            console.warn('Dashboard not available or showKvGrowthChart method not found');
            this.showToast('Chart functionality not available', 'error');
        }
    }

    /**
     * Handle external state changes
     */
    handleExternalStateChange(changes) {
        console.log('External state change received:', changes);

        let stateChanged = false;

        // Update internal state
        Object.keys(changes).forEach(key => {
            if (this.state.hasOwnProperty(key) && this.state[key] !== changes[key]) {
                this.state[key] = changes[key];
                stateChanged = true;
            }
        });

        if (stateChanged) {
            // Update UI to reflect changes
            this.updateUI();

            // Re-validate if sequence length changed
            if (changes.minSequenceLength !== undefined || changes.maxSequenceLength !== undefined) {
                this.validateSequenceLengthRange();
            }
        }
    }

    /**
     * Update memory controls visibility based on toggle state
     */
    updateMemoryControlsVisibility() {
        const kvCacheConfig = document.getElementById('kvCacheConfig');
        if (!kvCacheConfig) return;

        if (this.state.showTotalMemory) {
            // Use Bootstrap's collapse to show
            const bsCollapse = new bootstrap.Collapse(kvCacheConfig, { show: true });
        } else {
            // Use Bootstrap's collapse to hide
            const bsCollapse = new bootstrap.Collapse(kvCacheConfig, { hide: true });
        }
    }

    /**
     * Update UI components to reflect current state
     */
    updateUI() {
        // Update memory toggle
        const toggle = document.getElementById('showTotalMemoryToggle');
        if (toggle) {
            toggle.checked = this.state.showTotalMemory;
        }

        // Update KV growth chart toggle
        const kvToggle = document.getElementById('showKvGrowthToggle');
        if (kvToggle) {
            kvToggle.checked = this.state.showKvGrowthChart;
        }

        // Update KV dtype selector
        const dtypeSelector = document.getElementById('kvCacheDtype');
        if (dtypeSelector) {
            dtypeSelector.value = this.state.kvCacheDtype;
        }

        // Update sequence length inputs (convert from tokens to thousands)
        const minInput = document.getElementById('minSequenceLength');
        const maxInput = document.getElementById('maxSequenceLength');
        if (minInput) minInput.value = this.state.minSequenceLength / 1000;
        if (maxInput) maxInput.value = this.state.maxSequenceLength / 1000;

        // Update visibility
        this.updateMemoryControlsVisibility();
    }

    /**
     * Emit state change events for other components
     */
    emitStateChange(property, value, fullState) {
        const event = new CustomEvent('memoryControlsChange', {
            detail: {
                property: property,
                value: value,
                state: { ...fullState }
            }
        });
        document.dispatchEvent(event);
    }

    /**
     * Show toast notification
     */
    showToast(message, type = 'info') {
        // If dashboard has showToast method, use it
        if (this.dashboard && typeof this.dashboard.showToast === 'function') {
            this.dashboard.showToast(message, type);
        } else {
            // Fallback to console
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
    }

    /**
     * Get current state (public API)
     */
    getState() {
        return { ...this.state };
    }

    /**
     * Set state programmatically (public API)
     */
    setState(newState) {
        const oldState = { ...this.state };

        // Update state
        Object.assign(this.state, newState);

        // Update UI
        this.updateUI();

        // Validate if sequence length changed
        if (newState.minSequenceLength !== undefined || newState.maxSequenceLength !== undefined) {
            this.validateSequenceLengthRange();
        }

        // Emit change events for changed properties
        Object.keys(newState).forEach(key => {
            if (oldState[key] !== newState[key]) {
                this.emitStateChange(key, newState[key], this.state);
            }
        });
    }

    /**
     * Check if controls are in valid state
     */
    isValid() {
        return this.state.validationErrors.length === 0;
    }

    /**
     * Get validation errors
     */
    getValidationErrors() {
        return [...this.state.validationErrors];
    }

    /**
     * Cleanup method for removing event listeners
     */
    destroy() {
        console.log('Destroying MemoryControls...');

        // Remove all event listeners
        this.eventListeners.forEach(({ element, event, handler }) => {
            element.removeEventListener(event, handler);
        });
        this.eventListeners = [];

        // Destroy chart if it exists
        if (this.kvGrowthChart) {
            this.kvGrowthChart.destroy();
            this.kvGrowthChart = null;
        }

        // Reset state
        this.state.isInitialized = false;

        console.log('MemoryControls destroyed');
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MemoryControls;
}

// Make available globally for browser usage
if (typeof window !== 'undefined') {
    window.MemoryControls = MemoryControls;
}
