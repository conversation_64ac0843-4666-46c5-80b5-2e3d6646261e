/**
 * Interactive Controls Component for LLM Modeling Metrics Dashboard
 *
 * This component provides real-time interactive controls for:
 * - Precision selector with real-time roofline updates
 * - Batch size and sequence length sliders for timing recalculation
 * - Operational intensity range controls
 * - Model configuration presets for common scenarios
 */

class InteractiveControls {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/api',
            enableRealTimeUpdates: true,
            updateDelay: 500, // ms delay for real-time updates
            onParameterChange: null,
            onPrecisionChange: null,
            onPresetLoad: null,
            ...options
        };

        // State
        this.currentParameters = {
            precision: 'fp16',
            batchSize: 1,
            sequenceLength: 2048,
            operationalIntensityRange: { min: 0.01, max: 1000 },
            selectedPreset: null
        };

        this.mixedPrecisionConfig = {
            enabled: false,
            weightDtype: 'fp16',
            activationDtype: 'fp16',
            gradDtype: 'fp32',
            optimizerDtype: 'fp32'
        };

        this.updateTimer = null;
        this.isUpdating = false;

        // Model configuration presets
        this.modelPresets = {
            'small-inference': {
                name: 'Small Model Inference',
                description: 'Optimized for small models (7B) inference',
                config: {
                    batchSize: 1,
                    sequenceLength: 2048,
                    precision: 'fp16',
                    mixedPrecision: false,
                    operationalIntensityRange: { min: 0.1, max: 100 }
                }
            },
            'large-inference': {
                name: 'Large Model Inference',
                description: 'Optimized for large models (70B+) inference',
                config: {
                    batchSize: 1,
                    sequenceLength: 4096,
                    precision: 'fp16',
                    mixedPrecision: true,
                    mixedPrecisionConfig: {
                        weightDtype: 'fp8',
                        activationDtype: 'fp16',
                        gradDtype: 'fp32',
                        optimizerDtype: 'fp32'
                    },
                    operationalIntensityRange: { min: 0.01, max: 1000 }
                }
            },
            'training-small': {
                name: 'Small Model Training',
                description: 'Optimized for training small models',
                config: {
                    batchSize: 8,
                    sequenceLength: 2048,
                    precision: 'bf16',
                    mixedPrecision: true,
                    mixedPrecisionConfig: {
                        weightDtype: 'bf16',
                        activationDtype: 'bf16',
                        gradDtype: 'fp32',
                        optimizerDtype: 'fp32'
                    },
                    operationalIntensityRange: { min: 0.1, max: 500 }
                }
            },
            'training-large': {
                name: 'Large Model Training',
                description: 'Optimized for training large models',
                config: {
                    batchSize: 4,
                    sequenceLength: 4096,
                    precision: 'bf16',
                    mixedPrecision: true,
                    mixedPrecisionConfig: {
                        weightDtype: 'fp8',
                        activationDtype: 'bf16',
                        gradDtype: 'fp32',
                        optimizerDtype: 'fp32'
                    },
                    operationalIntensityRange: { min: 0.01, max: 1000 }
                }
            },
            'memory-constrained': {
                name: 'Memory Constrained',
                description: 'Optimized for memory-constrained environments',
                config: {
                    batchSize: 1,
                    sequenceLength: 1024,
                    precision: 'fp8',
                    mixedPrecision: true,
                    mixedPrecisionConfig: {
                        weightDtype: 'fp4',
                        activationDtype: 'fp8',
                        gradDtype: 'fp16',
                        optimizerDtype: 'fp16'
                    },
                    operationalIntensityRange: { min: 0.1, max: 100 }
                }
            },
            'high-throughput': {
                name: 'High Throughput',
                description: 'Optimized for maximum throughput',
                config: {
                    batchSize: 32,
                    sequenceLength: 1024,
                    precision: 'fp16',
                    mixedPrecision: false,
                    operationalIntensityRange: { min: 1, max: 1000 }
                }
            }
        };

        this.init();
    }

    async init() {
        try {
            console.log('Initializing Interactive Controls...');
            this.render();
            this.bindEvents();
            this.loadSavedState();
            console.log('Interactive Controls initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Interactive Controls:', error);
            this.showError('Failed to initialize interactive controls. Please refresh the page.');
        }
    }

    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }

        this.container.innerHTML = this.getInteractiveControlsHTML();
        this.initializeSliders();
    }

    getInteractiveControlsHTML() {
        return `
            <div class="interactive-controls-component">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-sliders-h me-1"></i>
                                Interactive Controls
                            </h6>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="enableRealTimeUpdates"
                                       ${this.options.enableRealTimeUpdates ? 'checked' : ''}>
                                <label class="form-check-label" for="enableRealTimeUpdates">
                                    Real-time Updates
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- Model Configuration Presets -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-magic me-1"></i>
                                Configuration Presets
                            </label>
                            <div class="row g-2">
                                ${Object.entries(this.modelPresets).map(([key, preset]) => `
                                    <div class="col-md-4">
                                        <button type="button" class="btn btn-outline-primary btn-sm w-100 preset-btn"
                                                data-preset="${key}" title="${preset.description}">
                                            <i class="fas fa-star me-1"></i>
                                            ${preset.name}
                                        </button>
                                    </div>
                                `).join('')}
                            </div>
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i>
                                Quick presets for common model configurations
                            </div>
                        </div>

                        <!-- Precision Controls -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-calculator me-1"></i>
                                    Precision Configuration
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableMixedPrecisionControls">
                                    <label class="form-check-label" for="enableMixedPrecisionControls">
                                        Mixed Precision
                                    </label>
                                </div>
                            </div>

                            <!-- Simple Precision -->
                            <div id="simplePrecisionControls">
                                <select class="form-select" id="precisionSelect">
                                    <option value="fp32">FP32 (32-bit float)</option>
                                    <option value="fp16" selected>FP16 (16-bit float)</option>
                                    <option value="bf16">BF16 (bfloat16)</option>
                                    <option value="tf32">TF32 (TensorFloat-32)</option>
                                    <option value="fp8">FP8 (8-bit float)</option>
                                    <option value="int8">INT8 (8-bit integer)</option>
                                    <option value="fp4">FP4 (4-bit float)</option>
                                </select>
                                <div class="form-text">Default precision for all components</div>
                            </div>

                            <!-- Mixed Precision Controls -->
                            <div id="mixedPrecisionControls" class="collapse">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="weightDtypeControl" class="form-label form-label-sm">Weights</label>
                                        <select class="form-select form-select-sm" id="weightDtypeControl">
                                            <option value="fp32">FP32</option>
                                            <option value="fp16" selected>FP16</option>
                                            <option value="bf16">BF16</option>
                                            <option value="fp8">FP8</option>
                                            <option value="fp4">FP4</option>
                                            <option value="int8">INT8</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="activationDtypeControl" class="form-label form-label-sm">Activations</label>
                                        <select class="form-select form-select-sm" id="activationDtypeControl">
                                            <option value="fp32">FP32</option>
                                            <option value="fp16" selected>FP16</option>
                                            <option value="bf16">BF16</option>
                                            <option value="fp8">FP8</option>
                                            <option value="int8">INT8</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="gradDtypeControl" class="form-label form-label-sm">Gradients</label>
                                        <select class="form-select form-select-sm" id="gradDtypeControl">
                                            <option value="fp32" selected>FP32</option>
                                            <option value="fp16">FP16</option>
                                            <option value="bf16">BF16</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="optimizerDtypeControl" class="form-label form-label-sm">Optimizer</label>
                                        <select class="form-select form-select-sm" id="optimizerDtypeControl">
                                            <option value="fp32" selected>FP32</option>
                                            <option value="fp16">FP16</option>
                                            <option value="bf16">BF16</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Parameter Controls -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-cogs me-1"></i>
                                Model Parameters
                            </label>

                            <!-- Batch Size Slider -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <label for="batchSizeSlider" class="form-label form-label-sm mb-0">
                                        Batch Size
                                    </label>
                                    <span class="badge bg-primary" id="batchSizeValue">1</span>
                                </div>
                                <input type="range" class="form-range" id="batchSizeSlider"
                                       min="1" max="128" value="1" step="1">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">1</small>
                                    <small class="text-muted">128</small>
                                </div>
                            </div>

                            <!-- Sequence Length Slider -->
                            <div class="mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <label for="sequenceLengthSlider" class="form-label form-label-sm mb-0">
                                        Sequence Length
                                    </label>
                                    <span class="badge bg-primary" id="sequenceLengthValue">2048</span>
                                </div>
                                <input type="range" class="form-range" id="sequenceLengthSlider"
                                       min="512" max="32768" value="2048" step="512">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">512</small>
                                    <small class="text-muted">32K</small>
                                </div>
                            </div>
                        </div>

                        <!-- Operational Intensity Range Controls -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-arrows-alt-h me-1"></i>
                                Operational Intensity Range
                            </label>

                            <!-- Min Intensity -->
                            <div class="mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <label for="minIntensitySlider" class="form-label form-label-sm mb-0">
                                        Minimum (FLOP/Byte)
                                    </label>
                                    <span class="badge bg-secondary" id="minIntensityValue">0.01</span>
                                </div>
                                <input type="range" class="form-range" id="minIntensitySlider"
                                       min="0.001" max="10" value="0.01" step="0.001">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">0.001</small>
                                    <small class="text-muted">10</small>
                                </div>
                            </div>

                            <!-- Max Intensity -->
                            <div class="mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <label for="maxIntensitySlider" class="form-label form-label-sm mb-0">
                                        Maximum (FLOP/Byte)
                                    </label>
                                    <span class="badge bg-secondary" id="maxIntensityValue">1000</span>
                                </div>
                                <input type="range" class="form-range" id="maxIntensitySlider"
                                       min="10" max="10000" value="1000" step="10">
                                <div class="d-flex justify-content-between">
                                    <small class="text-muted">10</small>
                                    <small class="text-muted">10K</small>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row g-2">
                            <div class="col-6">
                                <button type="button" class="btn btn-primary btn-sm w-100" id="applyChangesBtn">
                                    <i class="fas fa-check me-1"></i>
                                    Apply Changes
                                </button>
                            </div>
                            <div class="col-6">
                                <button type="button" class="btn btn-outline-secondary btn-sm w-100" id="resetControlsBtn">
                                    <i class="fas fa-undo me-1"></i>
                                    Reset to Defaults
                                </button>
                            </div>
                        </div>

                        <!-- Status Indicator -->
                        <div class="mt-3">
                            <div id="controlsStatus" class="d-flex align-items-center">
                                <div class="spinner-border spinner-border-sm me-2" role="status"
                                     id="controlsSpinner" style="display: none;">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                                <span id="controlsStatusText" class="text-muted">Ready</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    initializeSliders() {
        // Initialize all sliders with proper event handling
        const sliders = [
            { id: 'batchSizeSlider', valueId: 'batchSizeValue', property: 'batchSize' },
            { id: 'sequenceLengthSlider', valueId: 'sequenceLengthValue', property: 'sequenceLength' },
            { id: 'minIntensitySlider', valueId: 'minIntensityValue', property: 'operationalIntensityRange.min' },
            { id: 'maxIntensitySlider', valueId: 'maxIntensityValue', property: 'operationalIntensityRange.max' }
        ];

        sliders.forEach(slider => {
            const sliderElement = document.getElementById(slider.id);
            const valueElement = document.getElementById(slider.valueId);

            if (sliderElement && valueElement) {
                sliderElement.addEventListener('input', (e) => {
                    const value = parseFloat(e.target.value);
                    valueElement.textContent = this.formatSliderValue(slider.property, value);
                    this.updateParameter(slider.property, value);
                });
            }
        });
    }

    formatSliderValue(property, value) {
        switch (property) {
            case 'batchSize':
                return value.toString();
            case 'sequenceLength':
                return value >= 1024 ? `${(value / 1024).toFixed(1)}K` : value.toString();
            case 'operationalIntensityRange.min':
            case 'operationalIntensityRange.max':
                return value < 1 ? value.toFixed(3) : value.toString();
            default:
                return value.toString();
        }
    }

    bindEvents() {
        // Preset buttons
        document.querySelectorAll('.preset-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const presetKey = e.target.closest('.preset-btn').dataset.preset;
                this.loadPreset(presetKey);
            });
        });

        // Real-time updates toggle
        const realTimeToggle = document.getElementById('enableRealTimeUpdates');
        if (realTimeToggle) {
            realTimeToggle.addEventListener('change', (e) => {
                this.options.enableRealTimeUpdates = e.target.checked;
                this.saveState();
            });
        }

        // Mixed precision toggle
        const mixedPrecisionToggle = document.getElementById('enableMixedPrecisionControls');
        if (mixedPrecisionToggle) {
            mixedPrecisionToggle.addEventListener('change', (e) => {
                this.toggleMixedPrecision(e.target.checked);
            });
        }

        // Precision selectors
        const precisionSelect = document.getElementById('precisionSelect');
        if (precisionSelect) {
            precisionSelect.addEventListener('change', (e) => {
                this.updateParameter('precision', e.target.value);
            });
        }

        // Mixed precision selectors
        const mixedPrecisionSelectors = [
            'weightDtypeControl', 'activationDtypeControl',
            'gradDtypeControl', 'optimizerDtypeControl'
        ];

        mixedPrecisionSelectors.forEach(id => {
            const element = document.getElementById(id);
            if (element) {
                element.addEventListener('change', (e) => {
                    const property = id.replace('Control', '').replace('Dtype', 'Dtype');
                    this.updateMixedPrecisionParameter(property, e.target.value);
                });
            }
        });

        // Action buttons
        const applyBtn = document.getElementById('applyChangesBtn');
        const resetBtn = document.getElementById('resetControlsBtn');

        if (applyBtn) {
            applyBtn.addEventListener('click', () => {
                this.applyChanges();
            });
        }

        if (resetBtn) {
            resetBtn.addEventListener('click', () => {
                this.resetToDefaults();
            });
        }
    }

    updateParameter(property, value) {
        // Handle nested properties
        if (property.includes('.')) {
            const [parent, child] = property.split('.');
            if (!this.currentParameters[parent]) {
                this.currentParameters[parent] = {};
            }
            this.currentParameters[parent][child] = value;
        } else {
            this.currentParameters[property] = value;
        }

        // Validate intensity range
        if (property.includes('operationalIntensityRange')) {
            this.validateIntensityRange();
        }

        // Trigger real-time update if enabled
        if (this.options.enableRealTimeUpdates) {
            this.scheduleUpdate();
        }

        this.saveState();
    }

    updateMixedPrecisionParameter(property, value) {
        this.mixedPrecisionConfig[property] = value;

        if (this.options.enableRealTimeUpdates) {
            this.scheduleUpdate();
        }

        this.saveState();
    }

    validateIntensityRange() {
        const min = this.currentParameters.operationalIntensityRange.min;
        const max = this.currentParameters.operationalIntensityRange.max;

        if (min >= max) {
            this.showWarning('Minimum intensity must be less than maximum intensity');
            return false;
        }

        return true;
    }

    scheduleUpdate() {
        // Clear existing timer
        if (this.updateTimer) {
            clearTimeout(this.updateTimer);
        }

        // Schedule new update
        this.updateTimer = setTimeout(() => {
            this.triggerParameterChange();
        }, this.options.updateDelay);
    }

    triggerParameterChange() {
        if (this.isUpdating) return;

        this.isUpdating = true;
        this.setStatus('Updating...', true);

        const parameters = {
            ...this.currentParameters,
            mixedPrecision: this.mixedPrecisionConfig.enabled ? this.mixedPrecisionConfig : null
        };

        if (this.options.onParameterChange) {
            this.options.onParameterChange(parameters);
        }

        // Simulate update completion
        setTimeout(() => {
            this.isUpdating = false;
            this.setStatus('Ready', false);
        }, 1000);
    }

    loadPreset(presetKey) {
        const preset = this.modelPresets[presetKey];
        if (!preset) {
            console.error(`Preset '${presetKey}' not found`);
            return;
        }

        console.log(`Loading preset: ${preset.name}`);

        // Update current parameters
        this.currentParameters = { ...this.currentParameters, ...preset.config };

        // Update mixed precision config if provided
        if (preset.config.mixedPrecision && preset.config.mixedPrecisionConfig) {
            this.mixedPrecisionConfig = {
                enabled: true,
                ...preset.config.mixedPrecisionConfig
            };
        } else {
            this.mixedPrecisionConfig.enabled = preset.config.mixedPrecision || false;
        }

        // Update UI elements
        this.updateUIFromState();

        // Trigger callbacks
        if (this.options.onPresetLoad) {
            this.options.onPresetLoad(presetKey, preset);
        }

        this.showSuccess(`Loaded preset: ${preset.name}`);
        this.saveState();

        // Trigger update if real-time is enabled
        if (this.options.enableRealTimeUpdates) {
            this.scheduleUpdate();
        }
    }

    updateUIFromState() {
        // Update sliders
        const batchSizeSlider = document.getElementById('batchSizeSlider');
        const sequenceLengthSlider = document.getElementById('sequenceLengthSlider');
        const minIntensitySlider = document.getElementById('minIntensitySlider');
        const maxIntensitySlider = document.getElementById('maxIntensitySlider');

        if (batchSizeSlider) {
            batchSizeSlider.value = this.currentParameters.batchSize;
            document.getElementById('batchSizeValue').textContent = this.currentParameters.batchSize;
        }

        if (sequenceLengthSlider) {
            sequenceLengthSlider.value = this.currentParameters.sequenceLength;
            document.getElementById('sequenceLengthValue').textContent =
                this.formatSliderValue('sequenceLength', this.currentParameters.sequenceLength);
        }

        if (minIntensitySlider) {
            minIntensitySlider.value = this.currentParameters.operationalIntensityRange.min;
            document.getElementById('minIntensityValue').textContent =
                this.formatSliderValue('operationalIntensityRange.min', this.currentParameters.operationalIntensityRange.min);
        }

        if (maxIntensitySlider) {
            maxIntensitySlider.value = this.currentParameters.operationalIntensityRange.max;
            document.getElementById('maxIntensityValue').textContent =
                this.formatSliderValue('operationalIntensityRange.max', this.currentParameters.operationalIntensityRange.max);
        }

        // Update precision selectors
        const precisionSelect = document.getElementById('precisionSelect');
        if (precisionSelect) {
            precisionSelect.value = this.currentParameters.precision;
        }

        // Update mixed precision toggle and controls
        const mixedPrecisionToggle = document.getElementById('enableMixedPrecisionControls');
        if (mixedPrecisionToggle) {
            mixedPrecisionToggle.checked = this.mixedPrecisionConfig.enabled;
            this.toggleMixedPrecision(this.mixedPrecisionConfig.enabled);
        }

        // Update mixed precision selectors
        if (this.mixedPrecisionConfig.enabled) {
            const selectors = {
                'weightDtypeControl': 'weightDtype',
                'activationDtypeControl': 'activationDtype',
                'gradDtypeControl': 'gradDtype',
                'optimizerDtypeControl': 'optimizerDtype'
            };

            Object.entries(selectors).forEach(([elementId, property]) => {
                const element = document.getElementById(elementId);
                if (element && this.mixedPrecisionConfig[property]) {
                    element.value = this.mixedPrecisionConfig[property];
                }
            });
        }
    }

    toggleMixedPrecision(enabled) {
        this.mixedPrecisionConfig.enabled = enabled;

        const simplePrecisionControls = document.getElementById('simplePrecisionControls');
        const mixedPrecisionControls = document.getElementById('mixedPrecisionControls');

        if (enabled) {
            simplePrecisionControls.style.display = 'none';
            mixedPrecisionControls.classList.add('show');
        } else {
            simplePrecisionControls.style.display = 'block';
            mixedPrecisionControls.classList.remove('show');
        }

        if (this.options.onPrecisionChange) {
            this.options.onPrecisionChange(enabled ? this.mixedPrecisionConfig : { precision: this.currentParameters.precision });
        }
    }

    applyChanges() {
        this.setStatus('Applying changes...', true);
        this.triggerParameterChange();
    }

    resetToDefaults() {
        this.currentParameters = {
            precision: 'fp16',
            batchSize: 1,
            sequenceLength: 2048,
            operationalIntensityRange: { min: 0.01, max: 1000 },
            selectedPreset: null
        };

        this.mixedPrecisionConfig = {
            enabled: false,
            weightDtype: 'fp16',
            activationDtype: 'fp16',
            gradDtype: 'fp32',
            optimizerDtype: 'fp32'
        };

        this.updateUIFromState();
        this.saveState();
        this.showSuccess('Reset to default values');

        if (this.options.enableRealTimeUpdates) {
            this.scheduleUpdate();
        }
    }

    saveState() {
        try {
            const state = {
                currentParameters: this.currentParameters,
                mixedPrecisionConfig: this.mixedPrecisionConfig,
                enableRealTimeUpdates: this.options.enableRealTimeUpdates
            };
            localStorage.setItem('interactiveControlsState', JSON.stringify(state));
        } catch (error) {
            console.warn('Failed to save state to localStorage:', error);
        }
    }

    loadSavedState() {
        try {
            const savedState = localStorage.getItem('interactiveControlsState');
            if (savedState) {
                const state = JSON.parse(savedState);
                this.currentParameters = { ...this.currentParameters, ...state.currentParameters };
                this.mixedPrecisionConfig = { ...this.mixedPrecisionConfig, ...state.mixedPrecisionConfig };
                this.options.enableRealTimeUpdates = state.enableRealTimeUpdates !== undefined ?
                    state.enableRealTimeUpdates : this.options.enableRealTimeUpdates;

                this.updateUIFromState();
                console.log('Loaded saved state');
            }
        } catch (error) {
            console.warn('Failed to load saved state:', error);
        }
    }

    setStatus(message, loading = false) {
        const statusText = document.getElementById('controlsStatusText');
        const spinner = document.getElementById('controlsSpinner');

        if (statusText) {
            statusText.textContent = message;
        }

        if (spinner) {
            spinner.style.display = loading ? 'inline-block' : 'none';
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showWarning(message) {
        this.showNotification(message, 'warning');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'success'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    // Public API methods
    getCurrentParameters() {
        return {
            ...this.currentParameters,
            mixedPrecision: this.mixedPrecisionConfig.enabled ? this.mixedPrecisionConfig : null
        };
    }

    setParameters(parameters) {
        this.currentParameters = { ...this.currentParameters, ...parameters };
        if (parameters.mixedPrecision) {
            this.mixedPrecisionConfig = { ...this.mixedPrecisionConfig, ...parameters.mixedPrecision };
        }
        this.updateUIFromState();
        this.saveState();
    }

    getAvailablePresets() {
        return Object.keys(this.modelPresets);
    }

    destroy() {
        if (this.updateTimer) {
            clearTimeout(this.updateTimer);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = InteractiveControls;
}
