/**
 * Timing Dashboard Component
 * Provides comprehensive operator timing analysis and visualization
 */

class TimingDashboard {
    constructor() {
        this.timingData = null;
        this.bottleneckData = null;
        this.selectedHardware = null;
        this.sortColumn = 'execution_time_ms';
        this.sortDirection = 'desc';
        this.filterText = '';
        this.bottleneckFilter = 'all'; // 'all', 'compute', 'memory'

        this.initializeEventListeners();
    }

    initializeEventListeners() {
        // Sort controls
        $(document).on('click', '.timing-sort-btn', (e) => {
            const column = $(e.target).data('column');
            this.handleSort(column);
        });

        // Filter controls
        $(document).on('input', '#timingFilterText', (e) => {
            this.filterText = e.target.value.toLowerCase();
            this.renderTimingTable();
        });

        $(document).on('change', '#bottleneckFilter', (e) => {
            this.bottleneckFilter = e.target.value;
            this.renderTimingTable();
        });

        // Export functionality
        $(document).on('click', '#exportTimingData', () => {
            this.exportTimingData();
        });

        // Refresh timing analysis
        $(document).on('click', '#refreshTimingAnalysis', () => {
            this.refreshTimingAnalysis();
        });
    }

    async analyzeOperatorTiming(operators, hardwareId) {
        try {
            this.selectedHardware = hardwareId;

            // Show loading state
            this.showLoadingState();

            // Prepare operator configurations
            const operatorConfigs = operators.map(op => ({
                name: op.name,
                type: op.type,
                parameters: op.parameters || {}
            }));

            // Analyze timing
            const timingResponse = await fetch('/api/timing/analyze', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    operators: operatorConfigs,
                    hardware_id: hardwareId
                })
            });

            if (!timingResponse.ok) {
                throw new Error(`Timing analysis failed: ${timingResponse.statusText}`);
            }

            this.timingData = await timingResponse.json();

            // Analyze bottlenecks
            const bottleneckResponse = await fetch('/api/timing/bottlenecks', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    operators: operatorConfigs,
                    hardware_id: hardwareId
                })
            });

            if (!bottleneckResponse.ok) {
                throw new Error(`Bottleneck analysis failed: ${bottleneckResponse.statusText}`);
            }

            this.bottleneckData = await bottleneckResponse.json();

            // Render the dashboard
            this.renderDashboard();

        } catch (error) {
            console.error('Error analyzing operator timing:', error);
            this.showError('Failed to analyze operator timing: ' + error.message);
        }
    }

    renderDashboard() {
        const container = $('#timingDashboardContainer');

        const dashboardHtml = `
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">
                            <i class="fas fa-stopwatch me-2"></i>
                            Operator Timing Analysis
                        </h5>
                        <div class="btn-group btn-group-sm">
                            <button type="button" class="btn btn-outline-primary" id="refreshTimingAnalysis">
                                <i class="fas fa-sync-alt me-1"></i>
                                Refresh
                            </button>
                            <button type="button" class="btn btn-outline-success" id="exportTimingData">
                                <i class="fas fa-download me-1"></i>
                                Export
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Summary Statistics -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            ${this.renderSummaryStats()}
                        </div>
                    </div>

                    <!-- Bottleneck Analysis -->
                    <div class="row mb-4">
                        <div class="col-md-12">
                            ${this.renderBottleneckAnalysis()}
                        </div>
                    </div>

                    <!-- Filter and Sort Controls -->
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="input-group input-group-sm">
                                <span class="input-group-text">
                                    <i class="fas fa-search"></i>
                                </span>
                                <input type="text" class="form-control" id="timingFilterText"
                                       placeholder="Filter operators..." value="${this.filterText}">
                            </div>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select form-select-sm" id="bottleneckFilter">
                                <option value="all" ${this.bottleneckFilter === 'all' ? 'selected' : ''}>All Operators</option>
                                <option value="compute" ${this.bottleneckFilter === 'compute' ? 'selected' : ''}>Compute Bound</option>
                                <option value="memory" ${this.bottleneckFilter === 'memory' ? 'selected' : ''}>Memory Bound</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">
                                Showing ${this.getFilteredData().length} of ${this.timingData.length} operators
                            </small>
                        </div>
                    </div>

                    <!-- Timing Table -->
                    <div class="table-responsive">
                        ${this.renderTimingTable()}
                    </div>
                </div>
            </div>
        `;

        container.html(dashboardHtml);
    }

    renderSummaryStats() {
        if (!this.timingData || !this.bottleneckData) return '';

        const totalOperators = this.timingData.length;
        const computeBound = this.bottleneckData.compute_bound_operators.length;
        const memoryBound = this.bottleneckData.memory_bound_operators.length;
        const avgComputeUtil = this.bottleneckData.compute_utilization_avg.toFixed(1);
        const avgMemoryUtil = this.bottleneckData.memory_utilization_avg.toFixed(1);

        // Calculate total execution time
        const totalExecutionTime = this.timingData.reduce((sum, op) => sum + op.execution_time_ms, 0);

        return `
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-chart-bar me-2"></i>
                        Summary Statistics
                    </h6>
                    <div class="row">
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-primary mb-0">${totalOperators}</div>
                                <small class="text-muted">Total Operators</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-warning mb-0">${computeBound}</div>
                                <small class="text-muted">Compute Bound</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-info mb-0">${memoryBound}</div>
                                <small class="text-muted">Memory Bound</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-success mb-0">${avgComputeUtil}%</div>
                                <small class="text-muted">Avg Compute Util</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-success mb-0">${avgMemoryUtil}%</div>
                                <small class="text-muted">Avg Memory Util</small>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="text-center">
                                <div class="h4 text-dark mb-0">${totalExecutionTime.toFixed(2)}</div>
                                <small class="text-muted">Total Time (ms)</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderBottleneckAnalysis() {
        if (!this.bottleneckData) return '';

        const bottleneckColor = {
            'compute': 'warning',
            'memory': 'info',
            'balanced': 'success'
        };

        const color = bottleneckColor[this.bottleneckData.overall_bottleneck] || 'secondary';

        return `
            <div class="card border-0 bg-light">
                <div class="card-body">
                    <h6 class="card-title">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Bottleneck Analysis
                    </h6>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="text-center">
                                <span class="badge bg-${color} fs-6 px-3 py-2">
                                    ${this.bottleneckData.overall_bottleneck.toUpperCase()} BOUND
                                </span>
                                <div class="mt-2">
                                    <small class="text-muted">Overall Bottleneck</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-8">
                            <div class="recommendations">
                                <strong>Recommendations:</strong>
                                <ul class="mb-0 mt-1">
                                    ${this.bottleneckData.recommendations.map(rec =>
                                        `<li><small>${rec}</small></li>`
                                    ).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderTimingTable() {
        if (!this.timingData) return '<div class="text-center py-4">No timing data available</div>';

        const filteredData = this.getFilteredData();
        const sortedData = this.getSortedData(filteredData);

        const tableHtml = `
            <table class="table table-hover table-sm">
                <thead class="table-light">
                    <tr>
                        <th class="sortable" data-column="operator_name">
                            Operator Name
                            ${this.getSortIcon('operator_name')}
                        </th>
                        <th class="sortable text-end" data-column="execution_time_ms">
                            Execution Time (ms)
                            ${this.getSortIcon('execution_time_ms')}
                        </th>
                        <th class="sortable text-end" data-column="compute_time_ms">
                            Compute Time (ms)
                            ${this.getSortIcon('compute_time_ms')}
                        </th>
                        <th class="sortable text-end" data-column="memory_time_ms">
                            Memory Time (ms)
                            ${this.getSortIcon('memory_time_ms')}
                        </th>
                        <th class="sortable text-center" data-column="bottleneck_type">
                            Bottleneck
                            ${this.getSortIcon('bottleneck_type')}
                        </th>
                        <th class="sortable text-end" data-column="utilization_percent">
                            Utilization (%)
                            ${this.getSortIcon('utilization_percent')}
                        </th>
                        <th class="sortable text-end" data-column="operational_intensity">
                            Op. Intensity
                            ${this.getSortIcon('operational_intensity')}
                        </th>
                        <th class="text-center">Tensor Core</th>
                        <th class="text-center">Optimizations</th>
                    </tr>
                </thead>
                <tbody>
                    ${sortedData.map(op => this.renderTimingRow(op)).join('')}
                </tbody>
            </table>
        `;

        return tableHtml;
    }

    renderTimingRow(operator) {
        const bottleneckBadge = operator.bottleneck_type === 'compute'
            ? '<span class="badge bg-warning">Compute</span>'
            : '<span class="badge bg-info">Memory</span>';

        const tensorCoreBadge = operator.tensor_core_utilization
            ? '<span class="badge bg-success"><i class="fas fa-check"></i></span>'
            : '<span class="badge bg-secondary"><i class="fas fa-times"></i></span>';

        const utilizationColor = operator.utilization_percent >= 80 ? 'success' :
                                operator.utilization_percent >= 60 ? 'warning' : 'danger';

        const optimizationCount = operator.optimization_opportunities.length;
        const optimizationBadge = optimizationCount > 0
            ? `<span class="badge bg-primary" title="${operator.optimization_opportunities.join('; ')}">${optimizationCount}</span>`
            : '<span class="badge bg-secondary">0</span>';

        return `
            <tr>
                <td>
                    <strong>${operator.operator_name}</strong>
                    <br>
                    <small class="text-muted">
                        ${this.formatNumber(operator.flops)} FLOPS,
                        ${this.formatBytes(operator.memory_movement_bytes)}
                    </small>
                </td>
                <td class="text-end">
                    <strong>${operator.execution_time_ms.toFixed(3)}</strong>
                </td>
                <td class="text-end">
                    ${operator.compute_time_ms.toFixed(3)}
                </td>
                <td class="text-end">
                    ${operator.memory_time_ms.toFixed(3)}
                </td>
                <td class="text-center">
                    ${bottleneckBadge}
                </td>
                <td class="text-end">
                    <span class="badge bg-${utilizationColor}">
                        ${operator.utilization_percent.toFixed(1)}%
                    </span>
                </td>
                <td class="text-end">
                    ${operator.operational_intensity.toFixed(2)}
                </td>
                <td class="text-center">
                    ${tensorCoreBadge}
                </td>
                <td class="text-center">
                    ${optimizationBadge}
                </td>
            </tr>
        `;
    }

    getFilteredData() {
        if (!this.timingData) return [];

        return this.timingData.filter(op => {
            // Text filter
            const matchesText = !this.filterText ||
                op.operator_name.toLowerCase().includes(this.filterText);

            // Bottleneck filter
            const matchesBottleneck = this.bottleneckFilter === 'all' ||
                op.bottleneck_type === this.bottleneckFilter;

            return matchesText && matchesBottleneck;
        });
    }

    getSortedData(data) {
        return [...data].sort((a, b) => {
            let aVal = a[this.sortColumn];
            let bVal = b[this.sortColumn];

            // Handle string sorting
            if (typeof aVal === 'string') {
                aVal = aVal.toLowerCase();
                bVal = bVal.toLowerCase();
            }

            let comparison = 0;
            if (aVal < bVal) comparison = -1;
            if (aVal > bVal) comparison = 1;

            return this.sortDirection === 'asc' ? comparison : -comparison;
        });
    }

    getSortIcon(column) {
        if (this.sortColumn !== column) {
            return '<i class="fas fa-sort text-muted ms-1"></i>';
        }

        const icon = this.sortDirection === 'asc' ? 'fa-sort-up' : 'fa-sort-down';
        return `<i class="fas ${icon} text-primary ms-1"></i>`;
    }

    handleSort(column) {
        if (this.sortColumn === column) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            this.sortColumn = column;
            this.sortDirection = 'desc';
        }
        this.renderTimingTable();

        // Re-render just the table part
        $('.table-responsive').html(this.renderTimingTable());
    }

    formatNumber(num) {
        if (num >= 1e12) return (num / 1e12).toFixed(1) + 'T';
        if (num >= 1e9) return (num / 1e9).toFixed(1) + 'G';
        if (num >= 1e6) return (num / 1e6).toFixed(1) + 'M';
        if (num >= 1e3) return (num / 1e3).toFixed(1) + 'K';
        return num.toString();
    }

    formatBytes(bytes) {
        if (bytes >= 1e12) return (bytes / 1e12).toFixed(1) + 'TB';
        if (bytes >= 1e9) return (bytes / 1e9).toFixed(1) + 'GB';
        if (bytes >= 1e6) return (bytes / 1e6).toFixed(1) + 'MB';
        if (bytes >= 1e3) return (bytes / 1e3).toFixed(1) + 'KB';
        return bytes + 'B';
    }

    exportTimingData() {
        if (!this.timingData) {
            alert('No timing data to export');
            return;
        }

        const exportData = {
            hardware_id: this.selectedHardware,
            timestamp: new Date().toISOString(),
            summary: {
                total_operators: this.timingData.length,
                compute_bound: this.bottleneckData.compute_bound_operators.length,
                memory_bound: this.bottleneckData.memory_bound_operators.length,
                overall_bottleneck: this.bottleneckData.overall_bottleneck
            },
            timing_data: this.timingData,
            bottleneck_analysis: this.bottleneckData
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], {
            type: 'application/json'
        });

        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `timing_analysis_${this.selectedHardware}_${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    async refreshTimingAnalysis() {
        if (!this.selectedHardware || !this.timingData) {
            alert('No timing analysis to refresh');
            return;
        }

        // Extract operators from current timing data
        const operators = this.timingData.map(op => ({
            name: op.operator_name,
            type: 'generic', // We'll need to store this information
            parameters: {}
        }));

        await this.analyzeOperatorTiming(operators, this.selectedHardware);
    }

    showLoadingState() {
        const container = $('#timingDashboardContainer');
        container.html(`
            <div class="card shadow-sm">
                <div class="card-body text-center py-5">
                    <div class="spinner-border text-primary mb-3" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <h5>Analyzing Operator Timing...</h5>
                    <p class="text-muted">Computing hardware-aware timing analysis</p>
                </div>
            </div>
        `);
    }

    showError(message) {
        const container = $('#timingDashboardContainer');
        container.html(`
            <div class="card shadow-sm border-danger">
                <div class="card-body text-center py-5">
                    <i class="fas fa-exclamation-triangle text-danger fa-3x mb-3"></i>
                    <h5 class="text-danger">Analysis Error</h5>
                    <p class="text-muted">${message}</p>
                    <button type="button" class="btn btn-outline-primary" onclick="location.reload()">
                        <i class="fas fa-refresh me-2"></i>
                        Retry
                    </button>
                </div>
            </div>
        `);
    }

    show() {
        $('#timingDashboardContainer').show();
    }

    hide() {
        $('#timingDashboardContainer').hide();
    }
}

// Global instance
window.timingDashboard = new TimingDashboard();
