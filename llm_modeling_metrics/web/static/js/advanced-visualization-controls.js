/**
 * Advanced Visualization Controls Component for LLM Modeling Metrics Dashboard
 *
 * This component provides advanced visualization controls for:
 * - Roofline plot zoom and pan with operator label persistence
 * - Operator filtering and grouping controls
 * - Export functionality for plots and analysis data
 * - Sharing and bookmark functionality for analysis sessions
 */

class AdvancedVisualizationControls {
    constructor(containerId, options = {}) {
        this.containerId = containerId;
        this.container = document.getElementById(containerId);
        this.options = {
            apiBaseUrl: '/api',
            enableExport: true,
            enableSharing: true,
            enableBookmarks: true,
            onFilterChange: null,
            onExport: null,
            onShare: null,
            ...options
        };

        // State
        this.currentFilters = {
            operatorTypes: ['all'],
            hardwarePlatforms: ['all'],
            precisions: ['all'],
            performanceRange: { min: 0, max: 1000 },
            intensityRange: { min: 0.01, max: 1000 }
        };

        this.groupingOptions = {
            enabled: false,
            groupBy: 'type', // 'type', 'hardware', 'precision'
            showGroupLabels: true
        };

        this.zoomState = {
            x: { min: null, max: null },
            y: { min: null, max: null },
            isZoomed: false
        };

        this.bookmarks = [];
        this.currentSession = null;

        this.init();
    }

    async init() {
        try {
            console.log('Initializing Advanced Visualization Controls...');
            this.render();
            this.bindEvents();
            this.loadBookmarks();
            console.log('Advanced Visualization Controls initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Advanced Visualization Controls:', error);
            this.showError('Failed to initialize advanced controls. Please refresh the page.');
        }
    }

    render() {
        if (!this.container) {
            console.error(`Container with ID '${this.containerId}' not found`);
            return;
        }

        this.container.innerHTML = this.getAdvancedControlsHTML();
    }

    getAdvancedControlsHTML() {
        return `
            <div class="advanced-visualization-controls-component">
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="fas fa-tools me-1"></i>
                            Advanced Visualization Controls
                        </h6>
                    </div>
                    <div class="card-body">
                        <!-- Zoom and Pan Controls -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-search me-1"></i>
                                Zoom & Pan Controls
                            </label>
                            <div class="row g-2">
                                <div class="col-auto">
                                    <button type="button" class="btn btn-outline-primary btn-sm" id="resetZoomBtn">
                                        <i class="fas fa-search-minus me-1"></i>
                                        Reset Zoom
                                    </button>
                                </div>
                                <div class="col-auto">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" id="zoomToFitBtn">
                                        <i class="fas fa-expand-arrows-alt me-1"></i>
                                        Zoom to Fit
                                    </button>
                                </div>
                                <div class="col-auto">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="persistLabelsToggle" checked>
                                        <label class="form-check-label" for="persistLabelsToggle">
                                            Persist Labels
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Operator Filtering -->
                        <div class="mb-4">
                            <label class="form-label">
                                <i class="fas fa-filter me-1"></i>
                                Operator Filtering
                            </label>

                            <!-- Operator Types Filter -->
                            <div class="mb-3">
                                <label for="operatorTypesFilter" class="form-label form-label-sm">Operator Types</label>
                                <select id="operatorTypesFilter" class="form-select form-select-sm" multiple>
                                    <option value="all" selected>All Types</option>
                                    <option value="attention">Attention</option>
                                    <option value="mlp">MLP/Feed Forward</option>
                                    <option value="moe">Mixture of Experts</option>
                                    <option value="embedding">Embedding</option>
                                    <option value="normalization">Normalization</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>

                            <!-- Performance Range Filter -->
                            <div class="mb-3">
                                <label class="form-label form-label-sm">Performance Range (TFLOPS)</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="minPerformanceFilter" placeholder="Min" value="0" step="0.1">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="maxPerformanceFilter" placeholder="Max" value="1000" step="0.1">
                                    </div>
                                </div>
                            </div>

                            <!-- Intensity Range Filter -->
                            <div class="mb-3">
                                <label class="form-label form-label-sm">Intensity Range (FLOP/Byte)</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="minIntensityFilter" placeholder="Min" value="0.01" step="0.01">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="maxIntensityFilter" placeholder="Max" value="1000" step="1">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Grouping Controls -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <label class="form-label mb-0">
                                    <i class="fas fa-layer-group me-1"></i>
                                    Operator Grouping
                                </label>
                                <div class="form-check form-switch">
                                    <input class="form-check-input" type="checkbox" id="enableGroupingToggle">
                                    <label class="form-check-label" for="enableGroupingToggle">
                                        Enable
                                    </label>
                                </div>
                            </div>

                            <div id="groupingControls" class="collapse">
                                <div class="row g-2">
                                    <div class="col-6">
                                        <label for="groupBySelect" class="form-label form-label-sm">Group By</label>
                                        <select class="form-select form-select-sm" id="groupBySelect">
                                            <option value="type" selected>Operator Type</option>
                                            <option value="hardware">Hardware Platform</option>
                                            <option value="precision">Precision</option>
                                            <option value="performance">Performance Range</option>
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <div class="form-check mt-4">
                                            <input class="form-check-input" type="checkbox" id="showGroupLabelsToggle" checked>
                                            <label class="form-check-label" for="showGroupLabelsToggle">
                                                Show Group Labels
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Export Controls -->
                        ${this.options.enableExport ? `
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-download me-1"></i>
                                    Export Options
                                </label>
                                <div class="row g-2">
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-success btn-sm" id="exportChartBtn">
                                            <i class="fas fa-chart-line me-1"></i>
                                            Export Chart
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-info btn-sm" id="exportDataBtn">
                                            <i class="fas fa-table me-1"></i>
                                            Export Data
                                        </button>
                                    </div>
                                    <div class="col-auto">
                                        <button type="button" class="btn btn-outline-warning btn-sm" id="exportConfigBtn">
                                            <i class="fas fa-cog me-1"></i>
                                            Export Config
                                        </button>
                                    </div>
                                </div>

                                <!-- Export Format Options -->
                                <div class="mt-2">
                                    <div class="row g-2">
                                        <div class="col-4">
                                            <select class="form-select form-select-sm" id="chartFormatSelect">
                                                <option value="png">PNG</option>
                                                <option value="svg">SVG</option>
                                                <option value="pdf">PDF</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <select class="form-select form-select-sm" id="dataFormatSelect">
                                                <option value="csv">CSV</option>
                                                <option value="json">JSON</option>
                                                <option value="xlsx">Excel</option>
                                            </select>
                                        </div>
                                        <div class="col-4">
                                            <select class="form-select form-select-sm" id="configFormatSelect">
                                                <option value="json">JSON</option>
                                                <option value="yaml">YAML</option>
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        ` : ''}

                        <!-- Sharing and Bookmarks -->
                        ${this.options.enableSharing || this.options.enableBookmarks ? `
                            <div class="mb-4">
                                <label class="form-label">
                                    <i class="fas fa-share-alt me-1"></i>
                                    Sharing & Bookmarks
                                </label>
                                <div class="row g-2">
                                    ${this.options.enableSharing ? `
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-outline-primary btn-sm" id="shareSessionBtn">
                                                <i class="fas fa-share me-1"></i>
                                                Share Session
                                            </button>
                                        </div>
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-outline-secondary btn-sm" id="copyLinkBtn">
                                                <i class="fas fa-link me-1"></i>
                                                Copy Link
                                            </button>
                                        </div>
                                    ` : ''}
                                    ${this.options.enableBookmarks ? `
                                        <div class="col-auto">
                                            <button type="button" class="btn btn-outline-warning btn-sm" id="bookmarkSessionBtn">
                                                <i class="fas fa-bookmark me-1"></i>
                                                Bookmark
                                            </button>
                                        </div>
                                        <div class="col-auto">
                                            <div class="dropdown">
                                                <button class="btn btn-outline-info btn-sm dropdown-toggle" type="button"
                                                        id="bookmarksDropdown" data-bs-toggle="dropdown">
                                                    <i class="fas fa-history me-1"></i>
                                                    Bookmarks
                                                </button>
                                                <ul class="dropdown-menu" id="bookmarksList">
                                                    <li><span class="dropdown-item-text text-muted">No bookmarks yet</span></li>
                                                </ul>
                                            </div>
                                        </div>
                                    ` : ''}
                                </div>
                            </div>
                        ` : ''}

                        <!-- Apply Filters Button -->
                        <div class="d-grid">
                            <button type="button" class="btn btn-primary btn-sm" id="applyFiltersBtn">
                                <i class="fas fa-check me-1"></i>
                                Apply Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    bindEvents() {
        // Zoom and Pan Controls
        const resetZoomBtn = document.getElementById('resetZoomBtn');
        const zoomToFitBtn = document.getElementById('zoomToFitBtn');
        const persistLabelsToggle = document.getElementById('persistLabelsToggle');

        if (resetZoomBtn) {
            resetZoomBtn.addEventListener('click', () => this.resetZoom());
        }

        if (zoomToFitBtn) {
            zoomToFitBtn.addEventListener('click', () => this.zoomToFit());
        }

        if (persistLabelsToggle) {
            persistLabelsToggle.addEventListener('change', (e) => {
                this.setPersistLabels(e.target.checked);
            });
        }

        // Filter Controls
        const operatorTypesFilter = document.getElementById('operatorTypesFilter');
        const minPerformanceFilter = document.getElementById('minPerformanceFilter');
        const maxPerformanceFilter = document.getElementById('maxPerformanceFilter');
        const minIntensityFilter = document.getElementById('minIntensityFilter');
        const maxIntensityFilter = document.getElementById('maxIntensityFilter');

        if (operatorTypesFilter) {
            // Initialize Select2 if available
            if (typeof $ !== 'undefined' && $.fn.select2) {
                $(operatorTypesFilter).select2({
                    theme: 'bootstrap-5',
                    placeholder: 'Select operator types...',
                    allowClear: false,
                    closeOnSelect: false
                });
            }

            operatorTypesFilter.addEventListener('change', () => {
                this.updateFilters();
            });
        }

        [minPerformanceFilter, maxPerformanceFilter, minIntensityFilter, maxIntensityFilter].forEach(input => {
            if (input) {
                input.addEventListener('change', () => {
                    this.updateFilters();
                });
            }
        });

        // Grouping Controls
        const enableGroupingToggle = document.getElementById('enableGroupingToggle');
        const groupBySelect = document.getElementById('groupBySelect');
        const showGroupLabelsToggle = document.getElementById('showGroupLabelsToggle');

        if (enableGroupingToggle) {
            enableGroupingToggle.addEventListener('change', (e) => {
                this.toggleGrouping(e.target.checked);
            });
        }

        if (groupBySelect) {
            groupBySelect.addEventListener('change', (e) => {
                this.updateGrouping('groupBy', e.target.value);
            });
        }

        if (showGroupLabelsToggle) {
            showGroupLabelsToggle.addEventListener('change', (e) => {
                this.updateGrouping('showGroupLabels', e.target.checked);
            });
        }

        // Export Controls
        if (this.options.enableExport) {
            const exportChartBtn = document.getElementById('exportChartBtn');
            const exportDataBtn = document.getElementById('exportDataBtn');
            const exportConfigBtn = document.getElementById('exportConfigBtn');

            if (exportChartBtn) {
                exportChartBtn.addEventListener('click', () => this.exportChart());
            }

            if (exportDataBtn) {
                exportDataBtn.addEventListener('click', () => this.exportData());
            }

            if (exportConfigBtn) {
                exportConfigBtn.addEventListener('click', () => this.exportConfig());
            }
        }

        // Sharing and Bookmark Controls
        if (this.options.enableSharing) {
            const shareSessionBtn = document.getElementById('shareSessionBtn');
            const copyLinkBtn = document.getElementById('copyLinkBtn');

            if (shareSessionBtn) {
                shareSessionBtn.addEventListener('click', () => this.shareSession());
            }

            if (copyLinkBtn) {
                copyLinkBtn.addEventListener('click', () => this.copyShareLink());
            }
        }

        if (this.options.enableBookmarks) {
            const bookmarkSessionBtn = document.getElementById('bookmarkSessionBtn');

            if (bookmarkSessionBtn) {
                bookmarkSessionBtn.addEventListener('click', () => this.bookmarkSession());
            }
        }

        // Apply Filters Button
        const applyFiltersBtn = document.getElementById('applyFiltersBtn');
        if (applyFiltersBtn) {
            applyFiltersBtn.addEventListener('click', () => this.applyFilters());
        }
    }

    // Zoom and Pan Methods
    resetZoom() {
        this.zoomState = {
            x: { min: null, max: null },
            y: { min: null, max: null },
            isZoomed: false
        };

        // Trigger chart reset zoom if callback provided
        if (this.options.onZoomReset) {
            this.options.onZoomReset();
        }

        this.showSuccess('Zoom reset to default view');
    }

    zoomToFit() {
        // Calculate optimal zoom to fit all data points
        if (this.options.onZoomToFit) {
            this.options.onZoomToFit();
        }

        this.showSuccess('Zoomed to fit all data points');
    }

    setPersistLabels(persist) {
        if (this.options.onPersistLabelsChange) {
            this.options.onPersistLabelsChange(persist);
        }
    }

    // Filter Methods
    updateFilters() {
        const operatorTypesFilter = document.getElementById('operatorTypesFilter');
        const minPerformanceFilter = document.getElementById('minPerformanceFilter');
        const maxPerformanceFilter = document.getElementById('maxPerformanceFilter');
        const minIntensityFilter = document.getElementById('minIntensityFilter');
        const maxIntensityFilter = document.getElementById('maxIntensityFilter');

        this.currentFilters = {
            operatorTypes: operatorTypesFilter ? Array.from(operatorTypesFilter.selectedOptions).map(opt => opt.value) : ['all'],
            performanceRange: {
                min: minPerformanceFilter ? parseFloat(minPerformanceFilter.value) || 0 : 0,
                max: maxPerformanceFilter ? parseFloat(maxPerformanceFilter.value) || 1000 : 1000
            },
            intensityRange: {
                min: minIntensityFilter ? parseFloat(minIntensityFilter.value) || 0.01 : 0.01,
                max: maxIntensityFilter ? parseFloat(maxIntensityFilter.value) || 1000 : 1000
            }
        };

        // Validate ranges
        if (this.currentFilters.performanceRange.min >= this.currentFilters.performanceRange.max) {
            this.showWarning('Performance minimum must be less than maximum');
            return;
        }

        if (this.currentFilters.intensityRange.min >= this.currentFilters.intensityRange.max) {
            this.showWarning('Intensity minimum must be less than maximum');
            return;
        }
    }

    applyFilters() {
        this.updateFilters();

        if (this.options.onFilterChange) {
            this.options.onFilterChange(this.currentFilters);
        }

        this.showSuccess('Filters applied successfully');
    }

    // Grouping Methods
    toggleGrouping(enabled) {
        this.groupingOptions.enabled = enabled;

        const groupingControls = document.getElementById('groupingControls');
        if (groupingControls) {
            if (enabled) {
                groupingControls.classList.add('show');
            } else {
                groupingControls.classList.remove('show');
            }
        }

        if (this.options.onGroupingChange) {
            this.options.onGroupingChange(this.groupingOptions);
        }
    }

    updateGrouping(property, value) {
        this.groupingOptions[property] = value;

        if (this.options.onGroupingChange) {
            this.options.onGroupingChange(this.groupingOptions);
        }
    }

    // Export Methods
    async exportChart() {
        const format = document.getElementById('chartFormatSelect')?.value || 'png';

        try {
            if (this.options.onExport) {
                await this.options.onExport('chart', format);
            }
            this.showSuccess(`Chart exported as ${format.toUpperCase()}`);
        } catch (error) {
            console.error('Export chart failed:', error);
            this.showError('Failed to export chart');
        }
    }

    async exportData() {
        const format = document.getElementById('dataFormatSelect')?.value || 'csv';

        try {
            if (this.options.onExport) {
                await this.options.onExport('data', format);
            }
            this.showSuccess(`Data exported as ${format.toUpperCase()}`);
        } catch (error) {
            console.error('Export data failed:', error);
            this.showError('Failed to export data');
        }
    }

    async exportConfig() {
        const format = document.getElementById('configFormatSelect')?.value || 'json';

        try {
            const config = {
                filters: this.currentFilters,
                grouping: this.groupingOptions,
                zoom: this.zoomState,
                timestamp: new Date().toISOString()
            };

            const dataStr = format === 'yaml' ?
                this.convertToYAML(config) :
                JSON.stringify(config, null, 2);

            const blob = new Blob([dataStr], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `visualization-config.${format}`;
            link.click();
            URL.revokeObjectURL(url);

            this.showSuccess(`Configuration exported as ${format.toUpperCase()}`);
        } catch (error) {
            console.error('Export config failed:', error);
            this.showError('Failed to export configuration');
        }
    }

    // Sharing Methods
    async shareSession() {
        try {
            const sessionData = {
                filters: this.currentFilters,
                grouping: this.groupingOptions,
                zoom: this.zoomState,
                timestamp: new Date().toISOString()
            };

            // Generate share URL (this would typically involve a backend API)
            const shareId = this.generateShareId();
            const shareUrl = `${window.location.origin}${window.location.pathname}?share=${shareId}`;

            // Store session data (in a real implementation, this would be sent to backend)
            localStorage.setItem(`share_${shareId}`, JSON.stringify(sessionData));

            if (this.options.onShare) {
                this.options.onShare(shareUrl, sessionData);
            }

            // Copy to clipboard
            await navigator.clipboard.writeText(shareUrl);
            this.showSuccess('Share link copied to clipboard');
        } catch (error) {
            console.error('Share session failed:', error);
            this.showError('Failed to create share link');
        }
    }

    async copyShareLink() {
        try {
            const currentUrl = window.location.href;
            await navigator.clipboard.writeText(currentUrl);
            this.showSuccess('Current page link copied to clipboard');
        } catch (error) {
            console.error('Copy link failed:', error);
            this.showError('Failed to copy link');
        }
    }

    // Bookmark Methods
    bookmarkSession() {
        const bookmark = {
            id: this.generateBookmarkId(),
            name: `Session ${new Date().toLocaleString()}`,
            filters: { ...this.currentFilters },
            grouping: { ...this.groupingOptions },
            zoom: { ...this.zoomState },
            timestamp: new Date().toISOString()
        };

        this.bookmarks.push(bookmark);
        this.saveBookmarks();
        this.updateBookmarksList();

        this.showSuccess('Session bookmarked successfully');
    }

    loadBookmark(bookmarkId) {
        const bookmark = this.bookmarks.find(b => b.id === bookmarkId);
        if (!bookmark) {
            this.showError('Bookmark not found');
            return;
        }

        this.currentFilters = { ...bookmark.filters };
        this.groupingOptions = { ...bookmark.grouping };
        this.zoomState = { ...bookmark.zoom };

        this.updateUIFromState();
        this.applyFilters();

        this.showSuccess(`Loaded bookmark: ${bookmark.name}`);
    }

    deleteBookmark(bookmarkId) {
        this.bookmarks = this.bookmarks.filter(b => b.id !== bookmarkId);
        this.saveBookmarks();
        this.updateBookmarksList();

        this.showSuccess('Bookmark deleted');
    }

    updateBookmarksList() {
        const bookmarksList = document.getElementById('bookmarksList');
        if (!bookmarksList) return;

        if (this.bookmarks.length === 0) {
            bookmarksList.innerHTML = '<li><span class="dropdown-item-text text-muted">No bookmarks yet</span></li>';
            return;
        }

        bookmarksList.innerHTML = this.bookmarks.map(bookmark => `
            <li>
                <div class="dropdown-item d-flex justify-content-between align-items-center">
                    <div>
                        <div class="fw-bold">${bookmark.name}</div>
                        <small class="text-muted">${new Date(bookmark.timestamp).toLocaleString()}</small>
                    </div>
                    <div>
                        <button class="btn btn-sm btn-outline-primary me-1" onclick="advancedControls.loadBookmark('${bookmark.id}')">
                            <i class="fas fa-upload"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="advancedControls.deleteBookmark('${bookmark.id}')">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </div>
            </li>
        `).join('');
    }

    // Utility Methods
    generateShareId() {
        return Math.random().toString(36).substr(2, 9);
    }

    generateBookmarkId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    convertToYAML(obj) {
        // Simple YAML conversion (in a real implementation, use a proper YAML library)
        return JSON.stringify(obj, null, 2).replace(/"/g, '').replace(/,/g, '');
    }

    updateUIFromState() {
        // Update filter controls
        const operatorTypesFilter = document.getElementById('operatorTypesFilter');
        if (operatorTypesFilter) {
            Array.from(operatorTypesFilter.options).forEach(option => {
                option.selected = this.currentFilters.operatorTypes.includes(option.value);
            });
        }

        // Update range inputs
        const minPerformanceFilter = document.getElementById('minPerformanceFilter');
        const maxPerformanceFilter = document.getElementById('maxPerformanceFilter');
        const minIntensityFilter = document.getElementById('minIntensityFilter');
        const maxIntensityFilter = document.getElementById('maxIntensityFilter');

        if (minPerformanceFilter) minPerformanceFilter.value = this.currentFilters.performanceRange.min;
        if (maxPerformanceFilter) maxPerformanceFilter.value = this.currentFilters.performanceRange.max;
        if (minIntensityFilter) minIntensityFilter.value = this.currentFilters.intensityRange.min;
        if (maxIntensityFilter) maxIntensityFilter.value = this.currentFilters.intensityRange.max;

        // Update grouping controls
        const enableGroupingToggle = document.getElementById('enableGroupingToggle');
        const groupBySelect = document.getElementById('groupBySelect');
        const showGroupLabelsToggle = document.getElementById('showGroupLabelsToggle');

        if (enableGroupingToggle) {
            enableGroupingToggle.checked = this.groupingOptions.enabled;
            this.toggleGrouping(this.groupingOptions.enabled);
        }

        if (groupBySelect) groupBySelect.value = this.groupingOptions.groupBy;
        if (showGroupLabelsToggle) showGroupLabelsToggle.checked = this.groupingOptions.showGroupLabels;
    }

    saveBookmarks() {
        try {
            localStorage.setItem('visualizationBookmarks', JSON.stringify(this.bookmarks));
        } catch (error) {
            console.warn('Failed to save bookmarks:', error);
        }
    }

    loadBookmarks() {
        try {
            const saved = localStorage.getItem('visualizationBookmarks');
            if (saved) {
                this.bookmarks = JSON.parse(saved);
                this.updateBookmarksList();
            }
        } catch (error) {
            console.warn('Failed to load bookmarks:', error);
        }
    }

    showSuccess(message) {
        this.showNotification(message, 'success');
    }

    showWarning(message) {
        this.showNotification(message, 'warning');
    }

    showError(message) {
        this.showNotification(message, 'error');
    }

    showNotification(message, type = 'info') {
        // Create a simple toast notification
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'warning' ? 'warning' : 'success'} alert-dismissible fade show position-fixed`;
        toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
    }

    // Public API methods
    getCurrentFilters() {
        return { ...this.currentFilters };
    }

    getCurrentGrouping() {
        return { ...this.groupingOptions };
    }

    getCurrentZoomState() {
        return { ...this.zoomState };
    }

    setFilters(filters) {
        this.currentFilters = { ...this.currentFilters, ...filters };
        this.updateUIFromState();
    }

    setGrouping(grouping) {
        this.groupingOptions = { ...this.groupingOptions, ...grouping };
        this.updateUIFromState();
    }

    setZoomState(zoomState) {
        this.zoomState = { ...this.zoomState, ...zoomState };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AdvancedVisualizationControls;
}
