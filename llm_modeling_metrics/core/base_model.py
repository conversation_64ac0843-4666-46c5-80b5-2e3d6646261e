"""
Base model interface and data structures for LLM modeling metrics.
"""

from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple

from ..utils.caching import cached, get_cache_manager

if TYPE_CHECKING:
    from .operators import HardwareSpecs


@dataclass
class ParallelConfig:
    """Configuration for parallel execution strategies."""

    tensor_parallel_size: int = 1
    pipeline_parallel_size: int = 1
    data_parallel_size: int = 1

    # MoE-specific parallelism
    expert_parallel_size: int = 1
    expert_data_parallel_size: int = 1

    def __post_init__(self):
        """Validate parallel configuration parameters."""
        if self.tensor_parallel_size < 1:
            raise ValueError("tensor_parallel_size must be >= 1")
        if self.pipeline_parallel_size < 1:
            raise ValueError("pipeline_parallel_size must be >= 1")
        if self.data_parallel_size < 1:
            raise ValueError("data_parallel_size must be >= 1")
        if self.expert_parallel_size < 1:
            raise ValueError("expert_parallel_size must be >= 1")
        if self.expert_data_parallel_size < 1:
            raise ValueError("expert_data_parallel_size must be >= 1")


@dataclass
class ModelMetrics:
    """Comprehensive metrics for a language model."""

    # Basic model information
    model_name: str
    architecture: str

    # Parameter counts
    total_params: int
    attention_params: int
    mlp_params: int
    embedding_params: int

    # FLOP calculations
    flops_forward: int
    flops_per_token: int

    # Memory requirements (in bytes)
    memory_params: int
    memory_activations: int
    memory_total: int

    # Matrix shapes for different components
    attention_shapes: Dict[str, Tuple[int, ...]] = field(default_factory=dict)
    mlp_shapes: Dict[str, Tuple[int, ...]] = field(default_factory=dict)

    # Parallel configuration used
    parallel_config: Optional[ParallelConfig] = None

    # Analysis metadata
    sequence_length: int = 2048
    batch_size: int = 1
    timestamp: datetime = field(default_factory=datetime.now)

    # Additional metrics for MoE models
    experts_per_token: Optional[int] = None
    active_params_per_token: Optional[int] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary format."""
        result = {
            "model_name": self.model_name,
            "architecture": self.architecture,
            "total_params": self.total_params,
            "attention_params": self.attention_params,
            "mlp_params": self.mlp_params,
            "embedding_params": self.embedding_params,
            "flops_forward": self.flops_forward,
            "flops_per_token": self.flops_per_token,
            "memory_params": self.memory_params,
            "memory_activations": self.memory_activations,
            "memory_total": self.memory_total,
            "attention_shapes": self.attention_shapes,
            "mlp_shapes": self.mlp_shapes,
            "sequence_length": self.sequence_length,
            "batch_size": self.batch_size,
            "timestamp": self.timestamp.isoformat(),
        }

        if self.parallel_config:
            result["parallel_config"] = {
                "tensor_parallel_size": self.parallel_config.tensor_parallel_size,
                "pipeline_parallel_size": self.parallel_config.pipeline_parallel_size,
                "data_parallel_size": self.parallel_config.data_parallel_size,
                "expert_parallel_size": self.parallel_config.expert_parallel_size,
                "expert_data_parallel_size": self.parallel_config.expert_data_parallel_size,
            }

        if self.experts_per_token is not None:
            result["experts_per_token"] = self.experts_per_token
        if self.active_params_per_token is not None:
            result["active_params_per_token"] = self.active_params_per_token

        return result


class BaseModel(ABC):
    """
    Abstract base class for all LLM model implementations.

    This class defines the interface that all model implementations must follow
    for computing parameters, FLOPs, memory requirements, and matrix shapes.
    """

    def __init__(self, model_name: str, config: Optional[Any] = None):
        """
        Initialize the model with name and configuration.

        Args:
            model_name: Name of the model (e.g., 'meta-llama/Llama-2-7b-hf')
            config: Model configuration object (will be fetched if None)
        """
        self.model_name = model_name
        self.config = config
        if "text_config" in config:
            self.config = config["text_config"]
        self._parsed_config = {}

        if self.config is not None:
            self._parse_config()

    @abstractmethod
    def _parse_config(self) -> None:
        """Parse the model configuration and extract key parameters."""
        pass

    def init_model(self, precision: str = "bf16",
            weight_precision: str = None,
            activation_precision: str = None,
            kv_cache_precision: str = None,
    ) -> None:
        """Initialize the model with the given precision."""
        self.precision = precision
        self.create_attention_layer(precision, weight_precision, activation_precision, kv_cache_precision)

    def create_ffn_layer(self,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
    ) -> "FFNLayer":
        """Create an MLP layer for the model."""
        from .operators import FFNLayer, MoELayer

        # Create MLP operator to compute parameters
        self.ffn_layer = FFNLayer(
            hidden_size=self._parsed_config["hidden_size"],
            intermediate_size=self._parsed_config["intermediate_size"],
            precision=precision, 
            weight_precision=weight_precision,
            activation_precision=activation_precision,
        )
        if self._parsed_config["num_moe_layers"] > 0:
            self.moe_layer = MoELayer(
                hidden_size=self._parsed_config["hidden_size"],
                intermediate_size=self._parsed_config['moe_intermediate_size'],
                shared_experts=self._parsed_config['n_shared_experts'],
                routed_experts=self._parsed_config['n_routed_experts'],
                experts_per_token=self._parsed_config["num_experts_per_tok"],
                precision=precision, 
                weight_precision=weight_precision,
                activation_precision=activation_precision,
            )

    def create_attention_layer(self,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,) -> "AttentionLayer":
        """Create an attention layer for the model."""
        from .operators import AttentionLayer, MLAAttentionLayer

        if not hasattr(self, "_parsed_config") or not self._parsed_config:
            raise ValueError(
                "Model configuration not parsed. Call _parse_config() first."
            )

        hidden_size = self._parsed_config.get("hidden_size")
        num_heads = self._parsed_config.get("num_attention_heads")
        num_kv_heads = self._parsed_config.get("num_key_value_heads", num_heads)

        if hidden_size <= 0 or num_heads <= 0:
            raise ValueError(
                "Invalid attention configuration: hidden_size and num_attention_heads must be > 0"
            )

        # Check for MLA-specific parameters (DeepSeek V3 style)
        kv_lora_rank = self._parsed_config.get("kv_lora_rank")
        q_lora_rank = self._parsed_config.get("q_lora_rank")
        qk_rope_head_dim = self._parsed_config.get("qk_rope_head_dim", 64)
        qk_nope_head_dim = self._parsed_config.get("qk_nope_head_dim", 128)
        v_head_dim = self._parsed_config.get("v_head_dim", 128)

        if kv_lora_rank is not None and q_lora_rank is not None:
            # Multi-head Latent Attention (MLA) for DeepSeek V3
            self.attn_layer = MLAAttentionLayer(
                hidden_size=hidden_size,
                num_heads=num_heads,
                kv_lora_rank=kv_lora_rank,
                q_lora_rank=q_lora_rank,
                qk_rope_head_dim=qk_rope_head_dim,
                qk_nope_head_dim=qk_nope_head_dim,
                v_head_dim=v_head_dim,
                precision=precision,
                weight_precision=weight_precision,
                activation_precision=activation_precision,
                kv_cache_precision=kv_cache_precision,
            )
        else:
            # Standard multi-head attention (MHA/GQA)
            head_dim = self._parsed_config.get("head_dim", hidden_size // num_heads)

            self.attn_layer = AttentionLayer(
                hidden_size=hidden_size,
                num_heads=num_heads,
                num_kv_heads=num_kv_heads,
                head_dim=head_dim,
                precision=precision,
                weight_precision=weight_precision,
                activation_precision=activation_precision,
                kv_cache_precision=kv_cache_precision,
            )
        self.attn_op = self.attn_layer.attn

    def compute_attention_mem_flops(
        self,
        batch_size: int,
        sequence_length: int,
        kv_lens: int,
        precision: str = "bf16",
        weight_precision: str = None,
        activation_precision: str = None,
        kv_cache_precision: str = None,
    ):

        if not hasattr(self, "attn_op") or self.attn_op is None:
            self.create_attention_layer(
                precision, weight_precision, activation_precision, kv_cache_precision
            )

        attn = self.attn_op

        attn.set_shape(batch_size, sequence_length, kv_lens)

        layer_num = self._parsed_config.get("num_hidden_layers")
        attn_mem = attn.compute_memory_access_bytes() * layer_num
        attn_flops = attn.compute_flops() * layer_num
        return attn_mem, attn_flops

    @abstractmethod
    def compute_attention_params(self) -> int:
        """
        Compute the number of parameters in attention layers.

        Returns:
            Number of parameters in all attention layers
        """
        pass

    @abstractmethod
    def compute_mlp_params(self) -> int:
        """
        Compute the number of parameters in MLP/feed-forward layers.

        Returns:
            Number of parameters in all MLP layers
        """
        pass

    @abstractmethod
    def compute_embedding_params(self) -> int:
        """
        Compute the number of parameters in embedding layers.

        Returns:
            Number of parameters in embedding layers
        """
        pass

    @abstractmethod
    def compute_flops(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        kv_lens: Optional[int] = None,
    ) -> Dict[str, int]:
        """
        Compute FLOPs for forward pass.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            kv_lens: KV cache length for decode analysis (defaults to sequence_length if not specified)

        Returns:
            Dictionary with FLOP breakdown by component
        """
        pass

    @abstractmethod
    def compute_memory_requirements(
        self, sequence_length: int = 2048, batch_size: int = 1, dtype: str = "fp16"
    ) -> Dict[str, int]:
        """
        Compute memory requirements for the model.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Data type for calculations ('fp16', 'bf16', 'fp32', 'int8')

        Returns:
            Dictionary with memory breakdown by component
        """
        pass

    @abstractmethod
    def get_matrix_shapes(
        self, parallel_config: Optional[ParallelConfig] = None
    ) -> Dict[str, Any]:
        """
        Get matrix shapes for model operations under parallel configuration.

        Args:
            parallel_config: Parallel execution configuration

        Returns:
            Dictionary with matrix shapes for different operations
        """
        pass

    def get_total_params(self) -> int:
        """
        Get total number of parameters in the model.

        Returns:
            Total parameter count
        """
        return (
            self._get_cached_attention_params()
            + self._get_cached_mlp_params()
            + self._get_cached_embedding_params()
        )

    @cached(ttl=7200, use_disk=True)  # Cache for 2 hours
    def _get_cached_attention_params(self) -> int:
        """Cached version of compute_attention_params."""
        return self.compute_attention_params()

    @cached(ttl=7200, use_disk=True)  # Cache for 2 hours
    def _get_cached_mlp_params(self) -> int:
        """Cached version of compute_mlp_params."""
        return self.compute_mlp_params()

    @cached(ttl=7200, use_disk=True)  # Cache for 2 hours
    def _get_cached_embedding_params(self) -> int:
        """Cached version of compute_embedding_params."""
        return self.compute_embedding_params()

    def invalidate_cache(self) -> None:
        """Invalidate all cached computations for this model."""
        cache_manager = get_cache_manager()

        # Invalidate parameter caches
        cache_manager.computation_cache.invalidate(self._get_cached_attention_params)
        cache_manager.computation_cache.invalidate(self._get_cached_mlp_params)
        cache_manager.computation_cache.invalidate(self._get_cached_embedding_params)

        # Invalidate metrics cache for common configurations
        for seq_len in [512, 1024, 2048, 4096, 8192]:
            for batch_size in [1, 2, 4, 8]:
                cache_manager.computation_cache.invalidate(
                    self.get_metrics, seq_len, batch_size
                )

    @cached(ttl=3600, use_disk=True)  # Cache for 1 hour
    def get_metrics(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        parallel_config: Optional[ParallelConfig] = None,
        kv_lens: Optional[int] = None,
    ) -> ModelMetrics:
        """
        Get comprehensive model metrics.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            parallel_config: Parallel execution configuration
            kv_lens: KV cache length for decode analysis (defaults to sequence_length if not specified)

        Returns:
            ModelMetrics object with all computed metrics
        """
        # Compute parameter counts (these are cached individually)
        attention_params = self._get_cached_attention_params()
        mlp_params = self._get_cached_mlp_params()
        embedding_params = self._get_cached_embedding_params()
        total_params = attention_params + mlp_params + embedding_params

        # Compute FLOPs
        flops_breakdown = self.compute_flops(sequence_length, batch_size, kv_lens)
        flops_forward = sum(flops_breakdown.values())
        flops_per_token = flops_forward // sequence_length if sequence_length > 0 else 0

        # Compute memory requirements (using default fp16 for backward compatibility)
        memory_breakdown = self.compute_memory_requirements(
            sequence_length, batch_size, dtype="fp16"
        )
        memory_params = memory_breakdown.get("parameters", 0)
        memory_activations = memory_breakdown.get("activations", 0)
        # Sum only numeric values, excluding metadata like dtype and bytes_per_element
        # memory_total = sum(v for v in memory_breakdown.values() if isinstance(v, (int, float)))
        memory_total = memory_breakdown.get("total", 0)
        # Get matrix shapes
        shapes = self.get_matrix_shapes(parallel_config)
        attention_shapes = shapes.get("attention", {})
        mlp_shapes = shapes.get("mlp", {})

        # Determine architecture from config or model name
        architecture = getattr(self.config, "model_type", "unknown")
        if architecture == "unknown":
            # Try to infer from model name
            if "llama" in self.model_name.lower():
                architecture = "llama"
            elif "deepseek" in self.model_name.lower():
                architecture = "deepseek"
            elif "qwen" in self.model_name.lower():
                architecture = "qwen"

        return ModelMetrics(
            model_name=self.model_name,
            architecture=architecture,
            total_params=total_params,
            attention_params=attention_params,
            mlp_params=mlp_params,
            embedding_params=embedding_params,
            flops_forward=flops_forward,
            flops_per_token=flops_per_token,
            memory_params=memory_params,
            memory_activations=memory_activations,
            memory_total=memory_total,
            attention_shapes=attention_shapes,
            mlp_shapes=mlp_shapes,
            parallel_config=parallel_config,
            sequence_length=sequence_length,
            batch_size=batch_size,
        )

    def get_memory_breakdown_by_dtype(
        self,
        sequence_length: int = 2048,
        batch_size: int = 1,
        dtype: str = "fp16",
        include_kv_cache: bool = True,
    ) -> Dict[str, Any]:
        """
        Get detailed memory breakdown with dtype-specific calculations.

        Args:
            sequence_length: Input sequence length
            batch_size: Batch size
            dtype: Data type for calculations ('fp16', 'bf16', 'fp32', 'int8')
            include_kv_cache: Whether to include KV cache memory

        Returns:
            Dictionary with simplified memory breakdown for API response
        """
        from ..metrics.memory_calculator import MemoryCalculator

        # Get total parameters
        total_params = self.get_total_params()

        # Get config as dict
        config_dict = self._parsed_config if hasattr(self, "_parsed_config") else {}
        if not config_dict and self.config:
            config_dict = (
                self.config.__dict__
                if hasattr(self.config, "__dict__")
                else self.config
            )

        # Compute memory breakdown using MemoryCalculator
        memory_breakdown = MemoryCalculator.compute_total_memory_requirements(
            config_dict,
            total_params,
            sequence_length=sequence_length,
            batch_size=batch_size,
            precision=dtype,
            training=False,
            activation_checkpointing=False,
            include_kv_cache=include_kv_cache,
        )
        print(f"{memory_breakdown=}")

        # Extract the main memory components for API response
        parameters = memory_breakdown.get("param_parameters", 0)
        kv_cache = (
            memory_breakdown.get("kv_cache_total_kv_cache", 0)
            if include_kv_cache
            else 0
        )
        activations = memory_breakdown.get("activation_total", 0)
        total = memory_breakdown.get("total", 0)

        # Return simplified structure for API
        return {
            "parameters": parameters,
            "kv_cache": kv_cache,
            "activations": activations,
            "total": total,
        }

    def get_attention_mechanism_type(self) -> str:
        """
        Return the attention mechanism type for this model.

        Returns:
            String indicating attention mechanism type ('MHA', 'GQA', 'MLA', 'Unknown')
        """
        from ..metrics.memory_calculator import MemoryCalculator

        config_dict = (
            self.config.__dict__ if hasattr(self.config, "__dict__") else self.config
        )
        attention_info = MemoryCalculator.get_attention_mechanism_info(config_dict)

        return attention_info["type"]

    def validate_parallel_config(self, parallel_config: ParallelConfig) -> bool:
        """
        Validate that a parallel configuration is feasible for this model.

        Args:
            parallel_config: Parallel configuration to validate

        Returns:
            True if configuration is valid, False otherwise
        """
        # Basic validation - subclasses can override for model-specific checks
        if not hasattr(self, "_parsed_config"):
            return False

        # Check if dimensions are divisible by tensor parallel size
        hidden_size = self._parsed_config.get("hidden_size", 0)
        num_heads = self._parsed_config.get("num_attention_heads", 0)

        if hidden_size > 0 and hidden_size % parallel_config.tensor_parallel_size != 0:
            return False
        if num_heads > 0 and num_heads % parallel_config.tensor_parallel_size != 0:
            return False

        return True

    def __repr__(self):
        result_str = f'Model: {self.model_name}\n hidden_size: {self._parsed_config["hidden_size"]}\n num_layers: {self._parsed_config["num_hidden_layers"]}\n num_heads: {self._parsed_config["num_attention_heads"]}\n intermediate_size: {self._parsed_config["intermediate_size"]}\n vocab_size: {self._parsed_config["vocab_size"]}\n'
        return result_str