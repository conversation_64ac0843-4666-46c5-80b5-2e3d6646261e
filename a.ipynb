import numpy as np
import matplotlib.pyplot as plt
from typing import Dict, <PERSON>, Tuple, Any
import os
import sys
import torch

import seaborn as sns

sns.set_style("whitegrid")
sns.set_palette("husl")


# Add the package to path for imports

from llm_modeling_metrics import ModelFactory
from llm_modeling_metrics.core.operators import (
    HardwareSpecs,
    AttentionOperator,
    MLAAttentionOperator,
)

# models = {}

# # DeepSeek V3 with MLA Attention
# print("Loading DeepSeek V3 model...")
# models["DSv3_MLA"] = {
#     "model": ModelFactory.create_model("deepseek-ai/DeepSeek-V3"),
#     "name": "DSv3, 8K-32K",
#     "color": "blue",
#     "marker": "o",
#     "scale_factor": 1.0,  # Use realistic values
# }

# Qwen3 MoE (Qwen/Qwen3-235B-A22B)
# print("Loading Qwen3 MoE model...")
# models["Qwen3_MoE"] = {
#     "model": ModelFactory.create_model("Qwen/Qwen3-235B-A22B"),
#     "name": "Qwen3 MoE, 8K-32K",
#     "color": "blue",
#     "marker": "o",
#     "scale_factor": 1.0,
# }

# print("Loading Step-3 representative model...")
# models["Step3_MFA"] = {
#     "model": ModelFactory.create_model("stepfun-ai/step3"),
#     "name": "Step-3, 8K-32K",
#     "color": "red",
#     "marker": "*",
#     "scale_factor": 1.0,
# }

model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")

model.compute_active_params_per_token() / 1e9

from transformers import AutoConfig, AutoModelForCausalLM

config = AutoConfig.from_pretrained("Qwen/Qwen3-235B-A22B", trust_remote_code=True)
config.num_hidden_layers = 1

from torch import nn

# config = AutoConfig.from_pretrained("openai-community/gpt2", trust_remote_code=True)
# config.num_hidden_layers = 4
# model = AutoModelForCausalLM.from_config(config)
# print(model)

model = ModelFactory.create_model("deepseek-ai/DeepSeek-V3")
active_params = model.compute_active_params_per_token()

# model = ModelFactory.create_model("moonshotai/Kimi-K2-Instruct")
# active_params = model.compute_active_params_per_token()
# active_params / 1e9

print(model.config)
print(model)

model.__class__.__name__

